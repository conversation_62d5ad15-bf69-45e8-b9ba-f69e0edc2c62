targetScope = 'managementGroup'

//Parameters
param policySource string 
param policyCategory string 
param assignmentEnforcementMode string 

// Variables
var sandbinitiativeName = 'ALZ_Sandbox'
var sandbassignmentName = 'ALZ_Sandbox'
var SAGOV01file = json(loadTextContent(('./Decommissioned/DE_policy/Deny_Resource_Deployment.json')))


// Policy deffenitions
resource SAGOV01 'Microsoft.Authorization/policyDefinitions@2020-09-01' = {
  name: guid('custom','Deny vNet peering cross subscription')
  properties: SAGOV01file.properties
}

// RESOURCES
resource sandbinitiative 'Microsoft.Authorization/policySetDefinitions@2023-04-01' = {
  name: sandbinitiativeName
  properties: {
    policyType: 'Custom'
    displayName: sandbinitiativeName
    description: '${sandbinitiativeName} via ${policySource}'
    metadata: {
      category: policyCategory
      source: policySource
      version: '0.0.1'
    }
    policyDefinitions: [
      {
        // SA.GOV.01.v01 Deny vNet peering cross subscription
        policyDefinitionId: SAGOV01.id
        policyDefinitionReferenceId: 'SA.GOV.01.v01 Deny vNet peering cross subscription'
      }/*
      {
        // SA.GOV.02.v01 Not allowed resource types
        //op dit moment kan je niet deployen in deze sub, je moet dus allowed resources toevoegen voor dat je hier iets deployed
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/6c112d4e-5bc7-47ae-a041-ea2d9dccd749'
        policyDefinitionReferenceId: 'SA.GOV.02.v01 Not allowed resource types'
        parameters: {}
      }*/
    ]
  }
}

resource sandbassignment 'Microsoft.Authorization/policyAssignments@2023-04-01' = {
      name: sandbassignmentName
      location: 'west europe'
      identity:{
       type:'SystemAssigned'
      }
      properties: {
        displayName: sandbassignmentName
        description: '${sandbassignmentName} via ${policySource}'
        enforcementMode: assignmentEnforcementMode
        metadata: {
          source: policySource
          version: '0.0.1'
        }
        policyDefinitionId: sandbinitiative.id      
      }
}
