using 'infra-azure.bicep' /*TODO: Provide a path to a bicep template*/

param location = 'westeurope'
param regio = 'weu'
param omgeving = 'p'
param klantCode = 't09'
param workload = 'infra-azure'


param vnetSettings = {
  addressPrefixes: [
    '**********/27'
  ]
  subnets: [
    {
      function: 'infra-azure'
      addressPrefix: '**********/27'
    }
  ]
  dnsServers: [
    '************'
    '************'
  ]
}

param hubData = {
  resourceGroupName: 't09-rg-connectivity-p-network-weu'
  vnetName: 't09-vnet-connectivity-p-weu-01'
  subscriptionId: '429d0cf1-1f06-4bf4-bdf5-413e572883d3'
  remoteAddressSpace: [
    '***********/27'
  ]
  firewallIp: '***********'
}

param remoteVirtualNetworkAddressSpace = '**********/26'

param beheerserverAddressPrefix = [
  '***********/32'
]

param mgtVm = {
  ipAddress: '***********'
  dataDisks: []
  vmSize: 'Standard_D4s_v4'
  volgNummer: '01'
  zone: '2'
  AcceleratedNetworking: 'true'
}

param deployLinuxmgtVm = false
 param linuxmgtVm = {
   ipAddress: '**********'
   dataDisks: []
   vmSize: 'Standard_D2s_v5'
   volgNummer: '01'
   zone: '2'
   AcceleratedNetworking: 'true'
 }

param lwpVm = {
  ipAddress: '***********'
  dataDisks: []
  vmSize: 'Standard_B2ms'
  volgNummer: '01'
  zone: '2'
  AcceleratedNetworking: 'false'
}

param helloidVm = {
  ipAddress: '***********'
  dataDisks: []
  vmSize: 'Standard_B2s'
  volgNummer: '01'
  zone: '2'
  AcceleratedNetworking: 'false'
}

param lwpstorage = {
  AzureServicesDefaultAction: 'Allow'
  BlobPublicAccess: 'true'
  pContainerNames: [
    'configurations'
    'apps'
    'intunefiles'
  ]
  pDataLakeEnabled: 'false'
  pStorageAccountName: 't09lwpuconfigs'
}

param lwpstorageContainerNames = [
  {
    name: 'apps'
    publicAccess: 'None' // Possible values: 'Blob', 'Container', or 'None'
  }
  {
    name: 'configurations'
    publicAccess: 'None'
  }
  {
    name: 'intunefiles'
    publicAccess: 'Blob'
  }
]

// param lwfastorage = {
//   AzureServicesDefaultAction: 'Allow'
//   BlobPublicAccess: 'true'
//   pContainerNames: 'flexapp'
//   pDataLakeEnabled: 'false'
//   pStorageAccountName: 't09lwflexapp'
// }

// param intunestorage = {
//   AzureServicesDefaultAction: 'Allow'
//   BlobPublicAccess: 'true'
//   pContainerNames: 'intunefiles'
//   pDataLakeEnabled: 'false'
//   pStorageAccountName: 't09intunestorage'
// }

param keyVaultIpAddress = '***********'

param adminUsername = 'ramadmin'
