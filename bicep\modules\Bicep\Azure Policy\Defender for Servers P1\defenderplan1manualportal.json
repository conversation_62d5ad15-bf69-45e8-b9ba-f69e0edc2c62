{"properties": {"displayName": "Configure Azure Defender for servers to be enabled", "policyType": "Custom", "mode": "All", "description": "Azure Defender for servers provides real-time threat protection for server workloads and generates hardening recommendations as well as alerts about suspicious activities.", "metadata": {"version": "1.0.0", "category": "Security Center"}, "parameters": {"effect": {"type": "string", "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["DeployIfNotExists", "Disabled"]}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Resources/subscriptions"}]}, "then": {"effect": "[parameters('effect')]", "details": {"type": "Microsoft.Security/pricings", "name": "VirtualMachines", "deploymentScope": "subscription", "existenceScope": "subscription", "roleDefinitionIds": ["/providers/Microsoft.Authorization/roleDefinitions/fb1c8493-542b-48eb-b624-b4c8fea62acd"], "existenceCondition": {"field": "Microsoft.Security/pricings/pricingTier", "equals": "Standard"}, "deployment": {"location": "westeurope", "properties": {"mode": "incremental", "parameters": {}, "template": {"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {}, "variables": {}, "resources": [{"type": "Microsoft.Security/pricings", "apiVersion": "2022-03-01", "name": "VirtualMachines", "properties": {"pricingTier": "Standard", "subPlan": "P1"}}, {"type": "Microsoft.Security/pricings", "apiVersion": "2022-03-01", "name": "arm", "properties": {"pricingTier": "standard"}}], "outputs": {}}}}}}}}, "id": "/providers/Microsoft.Authorization/policyDefinitions/8e86a5b6-b9bd-49d1-8e21-4bb8a0862222", "name": "8e86a5b6-b9bd-49d1-8e21-4bb8a0862222"}