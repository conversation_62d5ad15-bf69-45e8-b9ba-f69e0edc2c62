@maxLength(3)
@minLength(3)
@description('klantCode van 3 karakters')
param klantCode string

@maxLength(3)
@minLength(3)
@description('Functie code voor de vm van 3 karakters, bijvoorbeeld dcr voor domain controller')
param functie string

@allowed([ 'o', 't', 'a', 'p' ])
@description('Beschrijft de omgeving, o voor ontwikkel, t voor test, a voor acceptatie, p voor productie')
param omgeving string

@allowed([ 'westeurope' ])
@description('De azure location voor de resources')
param location string

@description('ip addres voor de vm')
param ipAddress string

@description('Subnet id voor de vm/nic')
param subnetId string

@description('Vm sku')
param vmSize string

@description('Username voor het admin account')
param adminUsername string

@secure()
param adminPassword string

@allowed(['1', '2', '3'])
@description('De Zone waar de vm in geplaats wordt')
param vmZone string

@minLength(2)
@maxLength(2)
@description('Volgnummer voor de vm naam, bijvoorbeeld 01')
param volgNummer string

@description('''Array met de datadisk informatie, bijvoorbeeld:
[
  {
    datadiskNummer: 1
    datadiskSize: 32
    caching: 'None'
  }
  {
    datadiskNummer: 2
    datadiskSize: 64
    caching: 'Read'
  }
]
''')
param dataDisks array

@description('Id van de application security group')
param asgId string

@description('Wanneer deze op false staat, zal er een nieuwe vm worden aangemaakt. Bij true wordt er een vm aangemaakt met de bestaande disks, alleen mogelijk bij ZRS disken')
param recoveryMode bool = false
param AcceleratedNetworking bool


var vmName = '${klantCode}-vm-${functie}-w${omgeving}${volgNummer}'
var vmSku = 'standard-gen2'
var extensionName = 'SqlIaaSAgent'
var extensionPublisher = 'Microsoft.SqlServer.Management'
var extensionVersion = '2.0'

resource nic 'Microsoft.Network/networkInterfaces@2022-07-01' = {
  name: '${vmName}-nic'
  location: location
  properties: {
    ipConfigurations: [
      {
        name: 'ipconfig1'
        properties: {
          privateIPAddress: ipAddress
          privateIPAllocationMethod: 'Static'
          subnet: {
            id: subnetId
          }
          applicationSecurityGroups: [
            {
              id: asgId
              location: location
            }
          ]
        }
      }
    ]

    dnsSettings: {
      dnsServers: []
    }
    enableAcceleratedNetworking: AcceleratedNetworking
    enableIPForwarding: false
  }
}


resource osDisk 'Microsoft.Compute/disks@2022-07-02' existing = if(recoveryMode) {
  name: '${vmName}-osdisk'
}

resource vm 'Microsoft.Compute/virtualMachines@2022-08-01' = {
  dependsOn: [ nic ]
  name: vmName
  location: location
  properties: {
    hardwareProfile: {
      vmSize: vmSize
    }
    securityProfile: {
      encryptionAtHost: true
      securityType:'TrustedLaunch'
      uefiSettings:{
        secureBootEnabled:true
        vTpmEnabled:true
      }
    }
    storageProfile: {
      imageReference: recoveryMode ? null : {
        publisher: 'microsoftsqlserver'
        offer: 'sql2022-ws2022'
        sku: vmSku
        version: 'latest'
      }
      osDisk: recoveryMode ?  {
          osType: 'Windows'
          name: '${vmName}-osdisk'
          createOption: 'Attach'
          caching: 'ReadWrite'
          managedDisk: {
            storageAccountType: 'Premium_LRS'
            id: osDisk.id
          }
         } : {
          osType: 'Windows'
          name: '${vmName}-osdisk'
          createOption: 'FromImage'
          caching: 'ReadWrite'
          managedDisk: {
            storageAccountType: 'Premium_LRS'
          }
         }
      dataDisks: [for disk in dataDisks: {
        name: '${vmName}-${disk.name}'
        managedDisk: {
          storageAccountType: 'Premium_LRS' //parameter
          id: dataDisk[disk.datadiskNummer - 1].id
        }
        lun: disk.datadiskNummer - 1
        caching: disk.caching
        createOption: 'Attach'

      }]

    }
    osProfile: recoveryMode ? null : {
      computerName: vmName
      adminUsername: adminUsername
      adminPassword: adminPassword
    } 
    networkProfile: {
      networkInterfaces: [
        {
          id: resourceId('Microsoft.Network/networkInterfaces', '${vmName}-nic')
          properties: {
            primary: true
          }
        }
      ]
    }
  }
  zones: [
    vmZone
  ]
}

resource vm_Extension 'Microsoft.Compute/virtualMachines/extensions@2022-03-01' = {
  parent: vm
  name: 'SqlIaasExtension'
  location: location
  properties: {
    publisher: extensionPublisher
    type: extensionName
    typeHandlerVersion: extensionVersion
  }
}

resource dataDisk 'Microsoft.Compute/disks@2022-07-02' = [for disk in dataDisks: {
  name: '${vmName}-${disk.name}'
  location: location
  sku: {
    name: 'Premium_LRS'
  }
  properties: {
    creationData: {
      createOption: 'Empty'
    }
    diskSizeGB: disk.datadiskSize

  }
  zones: [ 
    vmZone
  ]

}]
