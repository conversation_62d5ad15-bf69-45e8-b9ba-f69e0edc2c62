# Azure Hub-and-Spoke Network Deployment with FortiGate Integration

This Bicep template deploys a hub-and-spoke network setup in Azure, which includes resource groups for a FortiGate firewall, a primary network, and vulnerability management. The hub virtual network (VNet) and its peerings are created using modules, with configurable settings for gateway transit, forwarded traffic, and access controls.

## Contents

- [Parameters](#parameters)
- [Variables](#variables)
- [Resources](#resources)
- [Modules](#modules)
- [Usage](#usage)

## Parameters

| Parameter                       | Type     | Default      | Description |
|---------------------------------|----------|--------------|-------------|
| `location`                      | `string` | `westeurope` | Azure region for resource deployment. |
| `regio`                         | `string` | `weu`        | Short region identifier for resource naming. |
| `omgeving`                      | `string` | None         | Environment name (e.g., Dev, Test, Prod). |
| `klantCode`                     | `string` | None         | Unique client identifier for resource naming. |
| `vnetSettings`                  | `object` | None         | Contains VNet configuration, including address prefixes, DNS servers, and subnets. |
| `peerings`                      | `array`  | None         | Array of peerings to establish with the hub VNet. |
| `allowVirtualNetworkAccess`     | `bool`   | `true`       | Enables access to virtual networks for peerings. |
| `allowForwardedTraffic`         | `bool`   | `true`       | Allows forwarded traffic between peerings. |
| `pAllowGatewayTransit`          | `bool`   | `false`      | Enables gateway transit for hub if set to `true`. |
| `pUseRemoteGateways`            | `bool`   | `false`      | Enables remote gateway usage for on-premises traffic via the hub. |
| `doNotVerifyRemoteGateways`     | `bool`   | `true`       | Disables gateway verification in remote networks. |
| `securityRules`                 | `array`  | None         | Network security rules to apply to the hub VNet. |

## Variables

- **`workload`**: Identifies the primary workload as `connectivity`, specifically for Microsoft Entra Domain Services (formerly Azure Active Directory Domain Services).
- **`rsgfortigateName`**: Name for the FortiGate resource group, derived from parameters.
- **`rsgnetworkName`**: Name for the network resource group, derived from parameters.
- **`vulnerabilityRsgName`**: Name for the vulnerability management resource group.

## Resources

- **Resource Groups**:
  - `fortigate_rg`: Contains resources for FortiGate firewall.
  - `network_rg`: Holds the main hub VNet and related resources.
  - `vulnerability_rg`: Dedicated to resources for vulnerability management.

## Modules

This template utilizes modules for creating the VNet, defining its subnets and DNS servers, applying security rules, and setting up peerings.

- **Hub VNet Module** (`hubVnet`): Creates the hub VNet, defines subnets, configures DNS servers, and applies network security rules.
- **Peering Module** (`hubpeering`): Sets up VNet peerings between the hub VNet and specified remote VNets with configurable access, traffic forwarding, and gateway transit settings.

### Key Peering Parameters

- `allowVirtualNetworkAccess`: Grants access between VNets in peering.
- `allowForwardedTraffic`: Allows traffic forwarding between VNets.
- `pAllowGatewayTransit`: Controls gateway transit for the hub VNet.
- `pUseRemoteGateways`: Enables the use of remote gateways in the hub if needed for on-premises traffic.

## Usage

1. **Define Parameters**: Set up the required parameters, such as `klantCode`, along with network configurations.
2. **Deploy Using Azure CLI**: 
   ```bash
   az deployment sub create --template-file .\connectivity.bicep --parameters .\connectivity.bicepparam --location westeurope 
  
