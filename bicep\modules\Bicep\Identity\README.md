# Azure Network and AADDS Deployment with Bicep

This Bicep template deploys an Azure Active Directory Domain Services (AADDS) setup within a structured network environment, including resource groups, a virtual network (VNet), and specific network security configurations. The deployment targets the subscription scope and relies on reusable modules to ensure a modular and consistent architecture.

## Contents

- [Parameters](#parameters)
- [Variables](#variables)
- [Resources](#resources)
- [Modules](#modules)
- [Usage](#usage)

## Parameters

| Parameter                        | Type     | Description |
|----------------------------------|----------|-------------|
| `location`                       | `string` | Location of resources. |
| `regio`                          | `string` | Region for resource deployment. |
| `omgeving`                       | `string` | Environment (e.g., Dev, Test, Prod). |
| `klantCode`                      | `string` | Client code to uniquely identify resources. |
| `childAdMembersAddressPrefixes`  | `array`  | IP prefixes for child AD members allowed in the network. |
| `managementAddressPrefixes`      | `array`  | IP prefixes allowed for management traffic. |
| `vnetSettings`                   | `object` | Object containing VNet configuration (address prefixes, DNS servers, subnets). |
| `hubData`                        | `object` | Information related to the hub network, including firewall IP. |
| `remoteVirtualNetworkAddressSpace` | `string` | Address space for remote virtual network. |
| `aaddsDomainConfigurationType`   | `string` | Configuration type for AADDS domain. |
| `aaddsDomainSecuritySettings`    | `object` | Security settings for AADDS. |
| `aaddsFilteredSync`              | `string` | Filtering configuration for AADDS sync. |
| `aaddsLdapsSettings`             | `object` | LDAPs settings for secure LDAP. |
| `aaddsNotificationSettings`      | `object` | Notification settings for AADDS. |
| `aaddsSku`                       | `string` | SKU for AADDS. |
| `aaddsSyncScope`                 | `string` | Sync scope for AADDS. |
| `DomainName`                     | `string` | Domain name for AADDS. |
| `scomgatewayAddressPrefix`       | `string` | Address prefix for SCOM Gateway access. |
| `workload`                       | `string` | Workload identifier for resource grouping. |

## Variables

- `rsgSpokeName`, `rsgaaddsName`, `vulnerabilityRsgName`: Names for resource groups based on parameters.
- `addsMember_tcp_ports`, `addsMember_udp_ports`: Ports required for AADDS communication over TCP and UDP.
- `securityRules`: A set of network security rules to manage inbound traffic for AADDS, management, and monitoring purposes.
- `routeTableName`, `routeName`: Defines routing configurations, including routes for AADDS and general traffic management.

## Resources

- **Resource Groups**:
  - `aadds_rg`: Holds AADDS resources.
  - `aadds_spoke_rg`: Holds the spoke VNet for AADDS.
  - `vulnerability_rg`: Holds resources related to vulnerability management.

## Modules

This template utilizes several modules to create reusable components for VNet, AADDS, and security configurations:

- **VNet Module** (`spokeVnetAadds`): Deploys a spoke VNet with DNS settings, subnets, security rules, and routing.
- **Application Security Group Module** (`asgaadds`): Creates an Application Security Group (ASG) for AADDS to securely communicate with specified AD members.
- **AADDS Module** (`aaddsmodule`): Configures AADDS settings, including security configurations, LDAPs, and sync scope.

## Usage

1. **Set Up Parameters**: Define required parameter values such as `klantCode`, and network configurations.
2. **Run Bicep Deployment**: Execute the Bicep template at the subscription level using Azure CLI:
   ```bash
   az deployment sub create --template-file identity.bicep  --parameters identity.parameters-p.bicepparam --location westeurope
