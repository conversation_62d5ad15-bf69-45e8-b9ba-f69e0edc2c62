targetScope = 'managementGroup'

//Parameters
param policySource string 
param policyCategory string 
param assignmentEnforcementMode string 

// Variables
var PFGOV01initiativeName = 'Enforce enhanced recovery and backup policies'
var PFGOV01assignmentName = 'PF.GOV.01.v01'
var PFSEC01initiativeName = 'Enforce recommended guardrails for Azure Key Vault'
var PFSEC01assignmentName = 'PF.SEC.01.v01'
var PFCOMP01initiativeName = 'Configure periodic checking for missing system updates'
var PFCOMP01assignmentName = 'PF.COMP.01.v01'

// Policy deffenition sets (initiative)

// RESOURCES
resource PFGOV01 'Microsoft.Authorization/policySetDefinitions@2023-04-01' = {
  name: PFGOV01initiativeName
  properties: {
    policyType: 'Custom'
    displayName: 'PF.GOV.01.v01 Enforce enhanced recovery and backup policies'
    description: '${PFGOV01initiativeName} via ${policySource}'
    metadata: {
      category: policyCategory
      source: policySource
      version: '0.0.1'
    }
    policyDefinitions: [
      {
        // PF.GOV.01.v01 [Preview]: Immutability must be enabled for backup vaults
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/2514263b-bc0d-4b06-ac3e-f262c0979018'
        policyDefinitionReferenceId: 'PF.GOV.01.v01 [Preview]: Immutability must be enabled for backup vaults'
        parameters: {}
      }
      {
        // PF.GOV.01.v01 [Preview]: Immutability must be enabled for Recovery Services vaults
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/d6f6f560-14b7-49a4-9fc8-d2c3a9807868'
        policyDefinitionReferenceId: 'PF.GOV.01.v01 [Preview]: Immutability must be enabled for Recovery Services vaults'
        parameters: {}
      }
      {
        // PF.GOV.01.v01 [Preview]: Multi-User Authorization (MUA) must be enabled for Backup Vaults.
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/c58e083e-7982-4e24-afdc-be14d312389e'
        policyDefinitionReferenceId: 'PF.GOV.01.v01 [Preview]: Multi-User Authorization (MUA) must be enabled for Backup Vaults'
        parameters: {}
      }
      {
        // PF.GOV.01.v01 [Preview]: Multi-User Authorization (MUA) must be enabled for Recovery Services Vaults.
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/c7031eab-0fc0-4cd9-acd0-4497bd66d91a'
        policyDefinitionReferenceId: 'PF.GOV.01.v01 [Preview]: Multi-User Authorization (MUA) must be enabled for Recovery Services Vaults'
        parameters: {}
      }
      {
        // PF.GOV.01.v01 [Preview]: Soft delete must be enabled for Recovery Services Vaults
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/31b8092a-36b8-434b-9af7-5ec844364148'
        policyDefinitionReferenceId: 'PF.GOV.01.v01 [Preview]: Soft delete must be enabled for Recovery Services Vaults'
        parameters: {}
      }
      {
        // PF.GOV.01.v01 [Preview]: Soft delete should be enabled for Backup Vaults
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/9798d31d-6028-4dee-8643-46102185c016'
        policyDefinitionReferenceId: 'PF.GOV.01.v01 [Preview]: Soft delete should be enabled for Backup Vaults'
        parameters: {}
      }
    ]
  }
}

resource PFSEC01 'Microsoft.Authorization/policySetDefinitions@2023-04-01' = {
  name: PFSEC01initiativeName
  properties: {
    policyType: 'Custom'
    displayName: 'PF.SEC.01.v01 Enforce recommended guardrails for Azure Key Vault'
    description: '${PFSEC01initiativeName} via ${policySource}'
    metadata: {
      category: policyCategory
      source: policySource
      version: '0.0.1'
    }
    policyDefinitions: [
      {
        // PF.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM keys should have an expiration date
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/1d478a74-21ba-4b9f-9d8f-8e6fced0eec5'
        policyDefinitionReferenceId: 'PF.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM keys should have an expiration date'
        parameters: {}
      }
      {
        // PF.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM Keys should have more than the specified number of days before expiration
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/ad27588c-0198-4c84-81ef-08efd0274653'
        policyDefinitionReferenceId: 'PF.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM Keys should have more than the specified number of days before expiration'
        parameters: {}
      }
      {
        // PF.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM keys using elliptic curve cryptography should have the specified curve names
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/e58fd0c1-feac-4d12-92db-0a7e9421f53e'
        policyDefinitionReferenceId: 'PF.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM keys using elliptic curve cryptography should have the specified curve names'
        parameters: {}
      }
      {
        // PF.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM keys using RSA cryptography should have a specified minimum key size
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/86810a98-8e91-4a44-8386-ec66d0de5d57'
        policyDefinitionReferenceId: 'PF.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM keys using RSA cryptography should have a specified minimum key size'
        parameters: {}
      }
      {
        // PF.SEC.01.v01 Azure Key Vault Managed HSM should have purge protection enabled
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/c39ba22d-4428-4149-b981-70acb31fc383'
        policyDefinitionReferenceId: 'PF.SEC.01.v01 Azure Key Vault Managed HSM should have purge protection enabled'
        parameters: {}
      }
      {
        // PF.SEC.01.v01 Azure Key Vault should have firewall enabled
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/55615ac9-af46-4a59-874e-391cc3dfb490'
        policyDefinitionReferenceId: 'PF.SEC.01.v01 Azure Key Vault should have firewall enabled'
        parameters: {}
      }
      {
        // PF.SEC.01.v01 Azure Key Vault should use RBAC permission model
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/12d4fa5e-1f9f-4c21-97a9-b99b3c6611b5'
        policyDefinitionReferenceId: 'PF.SEC.01.v01 Azure Key Vault should use RBAC permission model'
        parameters: {}
      }
      {
        // PF.SEC.01.v01 Certificates should be issued by the specified integrated certificate authority
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/8e826246-c976-48f6-b03e-619bb92b3d82'
        policyDefinitionReferenceId: 'PF.SEC.01.v01 Certificates should be issued by the specified integrated certificate authority'
        parameters: {}
      }/*
      {
        // PF.SEC.01.v01 Certificates should be issued by the specified non-integrated certificate authority
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/a22f4a40-01d3-4c7d-8071-da157eeff341'
        policyDefinitionReferenceId: 'PF.SEC.01.v01 Certificates should be issued by the specified non-integrated certificate authority'
        parameters: {}
      }//caCommonname missing 
      {
        // PF.SEC.01.v01 Certificates should have the specified lifetime action triggers
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/12ef42cb-9903-4e39-9c26-422d29570417'
        policyDefinitionReferenceId: 'PF.SEC.01.v01 Certificates should have the specified lifetime action triggers'
        parameters: {}
      }*/  //is missing the parameter(s) 'maximumPercentageLife,minimumDaysBeforeExpiry' as defined in the policy definition
    ]
  }
}

resource PFCOMP01 'Microsoft.Authorization/policySetDefinitions@2023-04-01' = {
  name: PFCOMP01initiativeName
  properties: {
    policyType: 'Custom'
    displayName: 'PF.COMP.01.v01 Configure periodic checking for missing system updates'
    description: '${PFCOMP01initiativeName} via ${policySource}'
    metadata: {
      category: policyCategory
      source: policySource
      version: '0.0.1'
    }
    policyDefinitions: [
      {
        // PF.COMP.01.v01 Configure periodic checking for missing system updates on azure virtual machines (Windows)
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/59efceea-0c96-497e-a4a1-4eb2290dac15'
        policyDefinitionReferenceId: 'PF.COMP.01.v01 Configure periodic checking for missing system updates on azure virtual machines (Windows)'
        parameters: {
          osType: {
            value: 'Windows'
          }    
        }
      }
      {
        // PF.COMP.01.v01 Configure periodic checking for missing system updates on azure virtual machines (Linux)
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/59efceea-0c96-497e-a4a1-4eb2290dac15'
        policyDefinitionReferenceId: 'PF.COMP.01.v01 Configure periodic checking for missing system updates on azure virtual machines (Linux)'
        parameters: {
          osType: {
            value: 'Linux'
          }    
        }
      }
      {
        // PF.COMP.01.v01 Configure periodic checking for missing system updates on azure Arc-enabled servers (Windows)
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/bfea026e-043f-4ff4-9d1b-bf301ca7ff46'
        policyDefinitionReferenceId: 'PF.COMP.01.v01 Configure periodic checking for missing system updates on azure Arc-enabled servers (Windows)'
        parameters: {
          osType: {
            value: 'Windows'
          }    
        }
      }
      {
        // PF.COMP.01.v01 Configure periodic checking for missing system updates on azure Arc-enabled servers (Linux)
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/bfea026e-043f-4ff4-9d1b-bf301ca7ff46'
        policyDefinitionReferenceId: 'PF.COMP.01.v01 Configure periodic checking for missing system updates on azure Arc-enabled servers (Linux)'
        parameters: {
          osType: {
            value: 'Linux'
          }    
        }
      }
    ]
  }
}

// ASSIGNMENTS


resource PFGOV01assignment 'Microsoft.Authorization/policyAssignments@2023-04-01' = {
  name: PFGOV01assignmentName
  location: 'west europe'
  identity:{
   type:'SystemAssigned'
  }
  properties: {
    displayName: PFGOV01assignmentName
    description: '${PFGOV01assignmentName} via ${policySource}'
    enforcementMode: assignmentEnforcementMode
    metadata: {
      source: policySource
      version: '0.0.1'
    }
    policyDefinitionId: PFGOV01.id      
  }
}

resource PFSEC01assignment 'Microsoft.Authorization/policyAssignments@2023-04-01' = {
  name: PFSEC01assignmentName
  location: 'west europe'
  identity:{
   type:'SystemAssigned'
  }
  properties: {
    displayName: PFSEC01assignmentName
    description: '${PFSEC01assignmentName} via ${policySource}'
    enforcementMode: assignmentEnforcementMode
    metadata: {
      source: policySource
      version: '0.0.1'
    }
    policyDefinitionId: PFSEC01.id      
  }
}

resource PFCOMP01assignment 'Microsoft.Authorization/policyAssignments@2023-04-01' = {
  name: PFCOMP01assignmentName
  location: 'west europe'
  identity:{
   type:'SystemAssigned'
  }
  properties: {
    displayName: PFCOMP01assignmentName
    description: '${PFCOMP01assignmentName} via ${policySource}'
    enforcementMode: assignmentEnforcementMode
    metadata: {
      source: policySource
      version: '0.0.1'
    }
    policyDefinitionId: PFCOMP01.id      
  }
}
