# Azure Hub-Spoke Network Deployment.

This Bicep template facilitates the deployment of a hub-spoke network architecture on Azure, including integration with FortiGate for enhanced security. The template leverages reusable modules for creating and configuring virtual networks (VNets), resource groups, and peerings within a subscription.

## Related Documentation and Modules

For additional details on module usage, see:
- [Connectivity Modules Documentation](https://github.com/RAMInfotechnology/RAMIT-bicep-modules/blob/main/Bicep/Connectivity/README.md)
- [Identity Modules Documentation](https://github.com/RAMInfotechnology/RAMIT-bicep-modules/blob/main/Bicep/Identity/README.md)
