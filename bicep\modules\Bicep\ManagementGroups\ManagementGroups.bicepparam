using './ManagementGroups.bicep'

param rootManagementGroupName = '<klantcode>'

param rootLandingZone = [
  {
    id: rootManagementGroupName
    displayName: rootManagementGroupName
  }
]

param midManagementGroups = [
  {
    id: 'Platform'
    displayName: 'Platform'
    parentId: rootManagementGroupName
  }
  {
    id: 'Workloads'
    displayName: 'Workloads'
    parentId: rootManagementGroupName
  }
  {
    id: 'Decommissioned'
    displayName: 'Decommissioned'
    parentId: rootManagementGroupName
  }
  {
    id: 'Sandbox'
    displayName: 'Sandbox'
    parentId: rootManagementGroupName
  }
]

param platformLandingZones = [
  {
    id: 'Management'
    displayName: 'Management'
    parentId: midManagementGroups[0].id
  }
  {
    id: 'Identity'
    displayName: 'Identity'
    parentId: midManagementGroups[0].id
  }
  {
    id: 'Connectivity'
    displayName: 'Connectivity'
    parentId: midManagementGroups[0].id
  }
]

param WorkloadsLandingZones = [
  {
    id: 'Corp'
    displayName: 'Corp'
    parentId: midManagementGroups[1].id
  }
  {
    id: 'Online'
    displayName: 'Online'
    parentId: midManagementGroups[1].id
  }
]

