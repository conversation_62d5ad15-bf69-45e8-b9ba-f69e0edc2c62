# Log in first with az login if you're not using Cloud Shell
# Select the subscription with az account set --subscription $subscriptionId command

# to confirm
# Log in first with az login if you're not using Cloud Shell

az account list

# Deploy policy definition on mangementgroup

az deployment mg create --location <location> --management-group-id --template-file <path-to-template> --parameters <path-to-parameter-file>
