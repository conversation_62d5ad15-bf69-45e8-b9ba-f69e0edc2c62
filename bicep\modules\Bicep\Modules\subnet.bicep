param vnetName string
param subnetAddressPrefix string
param subnetName string

resource myvnet 'Microsoft.Network/virtualNetworks@2023-04-01' existing = {
  name: vnetName
}

resource subnet 'Microsoft.Network/virtualNetworks/subnets@2023-04-01' = {

  name: subnetName
  parent: myvnet
  properties:{
    addressPrefix:subnetAddressPrefix //Address prefix should **not** be overlapping with existing subnets
  }
}

