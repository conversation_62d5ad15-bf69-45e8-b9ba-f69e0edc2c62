@allowed([ 'westeurope' ])
@description('De azure location voor de resources')
param location string = 'westeurope'

@description('Het subnet id van het subnet waar de storage aan wordt gekoppeld')
param subnetId string

@description('Naam van de private dns zone waar de endpoint aan gekoppeld wordt')
param privateDnsZoneName string

@description('Id van de private dns zone')
param privateDnsZoneId string

param keyVaultName string 
param asgName string
param asgId string
param keyVaultTenantId string 

var keyVaultSkuFamily = 'A'
var keyVaultSkuName = 'standard'
var keyVaultenableSoftDelete = bool(true)
var keyVaultsoftDeleteRetentionInDays = int(90)
var keyVaultpublicNetworkAccess = 'Disabled'

@description('Het interne ip address die assigned wordt aan het storage account voor de private endpoint')
param keyVaultIpAddress string

resource keyVault 'Microsoft.KeyVault/vaults@2023-07-01' = {
name: keyVaultName
location: location
properties: {
  sku: {
    family: keyVaultSkuFamily
    name:  keyVaultSkuName
  }
  tenantId: keyVaultTenantId
  enableSoftDelete: keyVaultenableSoftDelete
  softDeleteRetentionInDays: keyVaultsoftDeleteRetentionInDays
  publicNetworkAccess: keyVaultpublicNetworkAccess
  enableRbacAuthorization:true
}
}

resource privateEndpoint 'Microsoft.Network/privateEndpoints@2022-07-01' = {
  name: '${keyVaultName}-endpoint'
  location: location

  properties: {

    privateLinkServiceConnections: [
      {
        name: '${keyVaultName}-privateLinkServiceConnections'
        properties: {
          groupIds: [
            'vault'
          ]
          privateLinkServiceId: keyVault.id
          privateLinkServiceConnectionState: {
            status: 'Approved'
            description: 'Auto-Approved'
            actionsRequired: 'None'
          }
        }
      }
    ]
    subnet: {
      id: subnetId
    }
    applicationSecurityGroups: [
      {
        id: asgId
        location: location
        properties: {}
        tags: {}
      }
    ]
    customNetworkInterfaceName: '${keyVaultName}-nic'
    ipConfigurations: [
      {
        name: 'ipconfig1'
        properties: {
          privateIPAddress: keyVaultIpAddress
          groupId: 'vault'
          memberName: 'default'
        }
      }
    ]
  }
}

resource keyVaultPrivateEndpointDns 'Microsoft.Network/privateEndpoints/privateDnsZoneGroups@2022-01-01' = {
  name: 'vault-PrivateDnsZoneGroup'
  parent: privateEndpoint
  properties: {
    privateDnsZoneConfigs: [
      {
        name: privateDnsZoneName
        properties: {
          privateDnsZoneId: privateDnsZoneId
        }
      }
    ]
  }
}

resource applicationsecuritygroup 'Microsoft.Network/applicationSecurityGroups@2022-07-01' = {
  name: asgName
  location: location
}
