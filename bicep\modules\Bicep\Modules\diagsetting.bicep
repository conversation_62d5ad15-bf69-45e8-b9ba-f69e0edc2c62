// Diagnostic setting to send AuditLogs and SignInLogs to the Log Analytics workspace
param existingdiagSetting string
param diagnosticSettingName string
param workspaceId string


resource diagSetting 'microsoft.aadiam/diagnosticSettings@2017-04-01' = if (existingdiagSetting == null) {
  name: diagnosticSettingName
  scope: tenant()
  properties: {
    workspaceId: workspaceId
    logs: [
      {
        category: 'AuditLogs'
        enabled: true
      }
      {
        category: 'SignInLogs'
        enabled: true
      }
    ]
  }
}

