targetScope = 'managementGroup'

//Parameters
param policySource string 
param policyCategory string 
param assignmentEnforcementMode string 

// Variables
var decominitiativeName = 'ALZ Decommissioned'
var decomassignmentName = 'ALZ Decommissioned'
var DEGOV01file = json(loadTextContent(('./Decommissioned/DE_policy/Deny_Resource_Deployment.json')))
var DEGOV02file = json(loadTextContent(('./Decommissioned/DE_policy/Deploy_Virtual_Machine_Auto_Shutdown_Schedule.json')))

// Policy deffenitions
resource DEGOV01 'Microsoft.Authorization/policyDefinitions@2020-09-01' = {
  name: guid('custom','Deny resource deployments')
  properties: DEGOV01file.properties
}

resource DEGOV02 'Microsoft.Authorization/policyDefinitions@2020-09-01' = {
  name: guid('custom','VM auto Shutdown')
  properties: DEGOV02file.properties
}

// RESOURCES
resource decominitiative 'Microsoft.Authorization/policySetDefinitions@2023-04-01' = {
  name: decominitiativeName
  properties: {
    policyType: 'Custom'
    displayName: decominitiativeName
    description: '${decominitiativeName} via ${policySource}'
    metadata: {
      category: policyCategory
      source: policySource
      version: '0.0.1'
    }
    policyDefinitions: [
      {
        // DE.GOV.01.v01 Deny resource deployments
        policyDefinitionId: DEGOV01.id
        policyDefinitionReferenceId: 'DE.GOV.01.v01 Deny resource deployments'
      }
      {
        // DE.GOV.02.v01 VM Auto Shutdown 
        policyDefinitionId: DEGOV02.id
        policyDefinitionReferenceId: 'DE.GOV.02.v01 VM Auto Shutdown'
      }
    ]
  }
}

resource decomassignment 'Microsoft.Authorization/policyAssignments@2023-04-01' = {
      name: decomassignmentName
      location: 'west europe'
      identity:{
       type:'SystemAssigned'
      }
      properties: {
        displayName: decomassignmentName
        description: '${decomassignmentName} via ${policySource}'
        enforcementMode: assignmentEnforcementMode
        metadata: {
          source: policySource
          version: '0.0.1'
        }
        policyDefinitionId: decominitiative.id      
      }
}
