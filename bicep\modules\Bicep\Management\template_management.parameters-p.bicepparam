using 'management.bicep' /*TODO: Provide a path to a bicep template*/

param location = 'westeurope'
param regio = 'weu'
param omgeving = 'p'
param klantCode = 'xxx'
param workload = 'management'


param vnetSettings = {
  addressPrefixes: [
    'x.x.x.x/x'
  ]
  subnets: [
    {
      function: 'management'
      addressPrefix: 'x.x.x.x/x'
    }
  ]
  dnsServers: [
    'x.x.x.x'
    'x.x.x.x'
  ]
}

param hubData = {
  resourceGroupName: 'xxx-rg-connectivity-p-network-weu'
  vnetName: 'xxx-vnet-connectivity-p-weu-01'
  subscriptionId: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
  remoteAddressSpace: [
    'x.x.x.x/x'
  ]
  firewallIp: 'x.x.x.x'
}
param remoteVirtualNetworkAddressSpace = 'x.x.x.x/x'

param beheerserverAddressPrefix = [
  'x.x.x.x/x'
  'x.x.x.x/x'
]

param mgtVm = {
  ipAddress: 'x.x.x.x'
  dataDisks: []
  vmSize: 'Standard_D4s_v4'
  volgNummer: '01'
  zone: '2'
  AcceleratedNetworking: 'true'
}

param linuxmgtVm = {
  ipAddress: 'x.x.x.x'
  dataDisks: []
  vmSize: 'Standard_D2s_v5'
  volgNummer: '01'
  zone: '2'
  AcceleratedNetworking: 'true'
}

param lwpVm = {
  ipAddress: 'x.x.x.x'
  dataDisks: []
  vmSize: 'Standard_B2ms'
  volgNummer: '01'
  zone: '2'
  AcceleratedNetworking: 'false'
}

param helloidVm = {
  ipAddress: 'x.x.x.x'
  dataDisks: []
  vmSize: 'Standard_B2s'
  volgNummer: '01'
  zone: '2'
  AcceleratedNetworking: 'false'
}

param lwpstorage = {
  AzureServicesDefaultAction: 'Allow'
  BlobPublicAccess: 'true'
  pContainerNames: [
    'configurations'
    'apps'
  ]
  pDataLakeEnabled: 'false'
  pStorageAccountName: 'xxxlwpuconfigs'
}

param lwfastorage = {
  AzureServicesDefaultAction: 'Allow'
  BlobPublicAccess: 'true'
  pContainerNames: 'flexapp'
  pDataLakeEnabled: 'false'
  pStorageAccountName: 'xxxlwflexapp'
}

param intunestorage = {
  AzureServicesDefaultAction: 'Allow'
  BlobPublicAccess: 'true'
  pContainerNames: 'intunefiles'
  pDataLakeEnabled: 'false'
  pStorageAccountName: 'xxxintunestorage'
}

param keyVaultIpAddress = 'x.x.x.x'

param adminUsername = 'ramadmin'
