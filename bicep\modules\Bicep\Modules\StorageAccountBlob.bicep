targetScope = 'resourceGroup'

param pStorageAccountName string
param pLocation string
param pContainerNames array
param pDataLakeEnabled bool
param pAllowBlobPublicAccess bool
param pStorageKind string = 'StorageV2'
param pStorageSku string = 'Standard_LRS'
@allowed([
  'Allow'
  'Deny'
])
param pAllowAzureServicesDefaultAction string

resource storageAccount 'Microsoft.Storage/storageAccounts@2019-06-01' = {
  name: pStorageAccountName
  location: pLocation
  kind: pStorageKind
  sku: {
    name: pStorageSku
  }
  properties: {
    accessTier: 'Hot'
    isHnsEnabled: pDataLakeEnabled
    allowBlobPublicAccess: pAllowBlobPublicAccess
    minimumTlsVersion: 'TLS1_2'
    networkAcls: {
      bypass: 'AzureServices'
      defaultAction: pAllowAzureServicesDefaultAction
      virtualNetworkRules: []
      ipRules: []
    }
  } 
}

resource blobServices 'Microsoft.Storage/storageAccounts/blobServices@2022-09-01' = {
  parent: storageAccount
  name: 'default'
}

resource blobContainer 'Microsoft.Storage/storageAccounts/blobServices/containers@2019-06-01' = [for container in pContainerNames: {
  parent: blobServices
  name: container.name
  properties: {
    publicAccess: container.publicAccess
  }
}]

// Outputs
