param location string
param aaddsDomainConfigurationType string
param aaddsDomainName string
param aaddsDomainSecuritySettings object
param aaddsFilteredSync string
param aaddsLdapsSettings object
param aaddsNotificationSettings object
param aaddsReplicaSetsSubnet string
param aaddsSku string
param aaddsSyncScope string

//var aaddsName = '${klantCode}-aadds-${workload}-${omgeving}-${function}'

resource aadds 'Microsoft.AAD/domainServices@2022-12-01' ={
  name: aaddsDomainName
  location: location
  properties:{
    domainConfigurationType: aaddsDomainConfigurationType
    domainName: aaddsDomainName
    domainSecuritySettings: {
      channelBinding: aaddsDomainSecuritySettings.channelBinding
      kerberosArmoring: aaddsDomainSecuritySettings.kerberosArmoring
      kerberosRc4Encryption: aaddsDomainSecuritySettings.kerberosRc4Encryption
      ldapSigning: aaddsDomainSecuritySettings.ldapSigning
      ntlmV1: aaddsDomainSecuritySettings.ntlmV1
      syncKerberosPasswords: aaddsDomainSecuritySettings.syncKerberosPasswords
      syncNtlmPasswords: aaddsDomainSecuritySettings.syncNtlmPasswords
      syncOnPremPasswords: aaddsDomainSecuritySettings.syncOnPremPasswords
      tlsV1: aaddsDomainSecuritySettings.tlsV1
    }
    filteredSync: aaddsFilteredSync
    ldapsSettings: {
      externalAccess: aaddsLdapsSettings.externalAccess
      ldaps: aaddsLdapsSettings.ldaps
      pfxCertificate: aaddsLdapsSettings.pfxCertificate
      pfxCertificatePassword: aaddsLdapsSettings.pfxCertificatePassword
    }
    notificationSettings: {
      additionalRecipients: [
        aaddsNotificationSettings.additionalRecipients
      ]
      notifyDcAdmins: aaddsNotificationSettings.notifyDcAdmins
      notifyGlobalAdmins: aaddsNotificationSettings.notifyGlobalAdmins
    }
    replicaSets: [
      {
        location: location
        subnetId: aaddsReplicaSetsSubnet
      }
    ]
    sku: aaddsSku
    syncScope: aaddsSyncScope
  }
}
