using 'identity.bicep' 

param location = 'westeurope'
param regio = 'weu'
param omgeving = 'p'
param klantCode = 'xxx'
param workload = 'identity'

param childAdMembersAddressPrefixes = [
  'x.x.x.x/x'
]

param managementAddressPrefixes = [
  'x.x.x.x/x'
]

param vnetSettings = {
  addressPrefixes: [
    'x.x.x.x/x'
  ]
  subnets: [
    {
      function: 'aadds'
      addressPrefix: 'x.x.x.x/x'
    }
  ]
  dnsServers: [
    'x.x.x.x'
    'x.x.x.x'
  ]
}

param hubData = {
  resourceGroupName: 'xxx-rg-connectivity-p-network-weu'
  vnetName: 'xxx-vnet-connectivity-p-weu-01'
  subscriptionId: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
  remoteAddressSpace: [
    'x.x.x.x/x'
  ]
  firewallIp: 'x.x.x.x'
}

param remoteVirtualNetworkAddressSpace = 'x.x.x.x/x'

param aaddsDomainConfigurationType = 'FullySynced'

param aaddsDomainSecuritySettings = {
  channelBinding: 'Enabled'
  kerberosArmoring: 'Disabled'
  kerberosRc4Encryption: 'Enabled'
  ldapSigning: 'Enabled'
  syncKerberosPasswords: 'Enabled'
  syncNtlmPasswords: 'Enabled'
  syncOnPremPasswords: 'Enabled'
  ntlmV1: 'Disabled'
  tlsV1: 'Disabled'
}

param aaddsFilteredSync = 'Disabled'

param aaddsLdapsSettings = {
  externalAccess: 'Disabled'
  ldaps: 'Disabled'
  pfxCertificate: ''
  pfxCertificatePassword: ''
}

param aaddsNotificationSettings = {
  additionalRecipients: '<EMAIL>'
  notifyDcAdmins: 'Enabled'
  notifyGlobalAdmins: 'Enabled'
}

param aaddsSku = 'Standard'

param aaddsSyncScope = 'All'

param DomainName = 'internal.oosterpoort.org'

param scomgatewayAddressPrefix = 'x.x.x.x'
