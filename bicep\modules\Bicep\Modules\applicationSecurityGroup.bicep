param klantCode string
param function string
param location string = resourceGroup().location
param workload string
param omgeving string
param regio string

var asgName = '${klantCode}-asg-${workload}-${omgeving}-${function}-${regio}'

resource applicationsecuritygroup 'Microsoft.Network/applicationSecurityGroups@2022-07-01' = {
  name: asgName
  location: location
}
output id string = applicationsecuritygroup.id
output name string = applicationsecuritygroup.name
