@allowed([ 'westeurope' ])
@description('De azure location voor de resources')
param location string

@allowed([ 'o', 't', 'a', 'p' ])
@description('Beschrijft de omgeving, o voor ontwikkel, t voor test, a voor acceptatie, p voor productie')
param omgeving string

@description('name of the workload')
param workload string

@minLength(3)
@maxLength(3)
@description('Klant code van 3 karakters')
param klantCode string

var recoveryServiceName = '${klantCode}-rsv-${workload}-${omgeving}'
var skuName = 'RS0'
var skuTier = 'Standard'
var publicNetworkAccess = 'Enabled'

var scheduleRunTimes = ['01:00']
var timeZone = 'W. Europe Standard Time'

resource recoveryVault 'Microsoft.RecoveryServices/vaults@2023-06-01' = {
  name: recoveryServiceName
  location: location
  sku: {
    name: skuName
    tier: skuTier
  }
  properties: {
    monitoringSettings:{
      azureMonitorAlertSettings:{
        alertsForAllJobFailures: 'Enabled'
      }
      classicAlertSettings:{
        alertsForCriticalOperations:'Disabled'
      }
    }
    publicNetworkAccess: publicNetworkAccess
  }
}



// resource backupPolicySql 'Microsoft.RecoveryServices/vaults/backupPolicies@2023-01-01' = {
//   parent: recoveryVault
//   name: '${klantCode}-sql-backup-policy'
//   location: location
//   properties: {
//     backupManagementType: 'AzureWorkload'
//     workLoadType: 'SQLDataBase'
//     settings: {
//       issqlcompression: false
//       isCompression: false
//       timeZone: timeZone
//     }
//     subProtectionPolicy:[
//       {
//         policyType: 'Full'
//         schedulePolicy: {
//           schedulePolicyType: 'SimpleSchedulePolicyV2'
//           scheduleRunFrequency: 'Daily'
//           dailySchedule:{
//             scheduleRunTimes: [
//               '2023-07-14T02:00:00Z'
//             ]
//           }
//           // weeklySchedule: {
//           //   scheduleRunDays: [
//           //     'Sunday'
//           //   ]
//           //   scheduleRunTimes: [
//           //     '2023-07-14T02:00:00Z'
//           //   ]
//           // }
    
//         }
//         retentionPolicy: {
//           retentionPolicyType: 'LongTermRetentionPolicy'
//           dailySchedule: {
//             retentionDuration: {
//               durationType: 'Days'
//               count:30
//             }
//           }
//         }
//       }
//       {
//         policyType: 'Log'
//         schedulePolicy: {
//           schedulePolicyType: 'LogSchedulePolicy'
//           scheduleFrequencyInMins: 60
//         }
//         retentionPolicy: {
//           retentionPolicyType: 'SimpleRetentionPolicy'
//           retentionDuration: {
//             count: 30
//             durationType: 'Days'
//           }
//         }
//       }
//     ]
//   }
// }
resource backupPolicyV2 'Microsoft.RecoveryServices/vaults/backupPolicies@2023-01-01' = {
  parent: recoveryVault
  name: '${klantCode}-vm-backup-policy-V2'
  location: location
  properties: {
    backupManagementType: 'AzureIaasVM'
    policyType: 'V2'

    instantRpRetentionRangeInDays: 2
    schedulePolicy: {
      schedulePolicyType: 'SimpleSchedulePolicyV2'
      scheduleRunFrequency: 'Daily'
      dailySchedule: {
          scheduleRunTimes: scheduleRunTimes
      }

    }
    retentionPolicy: {
      retentionPolicyType: 'LongTermRetentionPolicy'
      dailySchedule: {
        retentionTimes: scheduleRunTimes
        retentionDuration: {
          count: 30
          durationType: 'Days'
        }
      }
      weeklySchedule: {
        daysOfTheWeek: [
          'Sunday'
        ]
        retentionTimes: scheduleRunTimes
        retentionDuration: {
          count: 4
          durationType: 'Weeks'
        }

      }
      monthlySchedule: {
        retentionDuration: {
          count: 12
          durationType: 'Months'
        }
        retentionTimes: [
          '01:00'
        ]
        retentionScheduleWeekly: {
          daysOfTheWeek: [
            'Sunday'
          ]   
          weeksOfTheMonth: [
            'First'
          ]
        }
        retentionScheduleFormatType: 'Weekly'     
      }
      yearlySchedule: {
        retentionDuration: {
          count: 7
          durationType: 'Years'
        }
        monthsOfYear: [
          'January'
        ]
        retentionTimes: scheduleRunTimes
        retentionScheduleFormatType: 'Weekly'
        retentionScheduleWeekly: {
          daysOfTheWeek: [
            'Sunday'
          ]
          weeksOfTheMonth: [
            'First'
          ]
        }
      }      
    }
    tieringPolicy: {
      ArchivedRP: {
        tieringMode: 'DoNotTier'
      }
    }
    timeZone: timeZone
  }
}

resource backupstorageConfig 'Microsoft.RecoveryServices/vaults/backupstorageconfig@2023-01-01' = {
  name: 'vaultstorageconfig'
  parent: recoveryVault
  properties: {
    storageType: 'ZoneRedundant'
    storageModelType: 'ZoneRedundant'

  }
}

// output backupPolicySqlId string = backupPolicySql.id
output backupPolicyV2Id string = backupPolicyV2.id
output recoveryServiceName string = recoveryServiceName
