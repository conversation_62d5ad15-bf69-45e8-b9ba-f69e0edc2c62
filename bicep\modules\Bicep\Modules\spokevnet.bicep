// Parameters

param klantCode string
param omgeving string
param location string = resourceGroup().location
param addressPrefixes array
param subnets array
param dnsServers array
param securityRules array
param workload string
param regio string
param routes array
param routeTableName string
param remoteVirtualNetworkAddressSpace string

@description('True for hub and false for spoke.')
param pAllowGatewayTransit bool = false

@description('set to true if the hub has gatewaysubnet and on-premise traffic from or to the spoke is needed.')
param pUseRemoteGateways bool = false

@description('Network ID remote VNET, required for peering')
param pHubVirtualNetworkId string 

// Variables

var vnetName = '${klantCode}-vnet-${workload}-${omgeving}-${regio}-01'
var subnetsArray = [for subnet in subnets: {
  name: '${klantCode}-sn-${subnet.function}'
  properties: {
    addressPrefix: subnet.addressPrefix
    routeTable: {
      id: routetable.id
    }
    networkSecurityGroup: {
      id: networkSecurityGroup.id
    }
    privateEndpointNetworkPolicies: 'Enabled'
  }
}]
var nsgName = '${klantCode}-nsg-${workload}-${omgeving}-${regio}-01'


// Resources

resource spokeVnet 'Microsoft.Network/virtualNetworks@2022-07-01' = {
  name: vnetName
  location: location
  properties: {
    addressSpace: {
      addressPrefixes: addressPrefixes
    }
    dhcpOptions: {
      dnsServers: dnsServers
    }
    subnets: subnetsArray
  }
}
resource localVirtualNetwork 'Microsoft.Network/virtualNetworks@2022-09-01' existing = {
  name: vnetName
}


resource spokeVirtualNetworkPeering 'Microsoft.Network/virtualNetworks/virtualNetworkPeerings@2023-04-01' = {
  dependsOn: [
    spokeVnet
  ]
  name: 'spokeVnetPeering'
  parent: localVirtualNetwork
  properties: {
    allowVirtualNetworkAccess: true
    allowForwardedTraffic: true
    allowGatewayTransit: pAllowGatewayTransit
    useRemoteGateways: pUseRemoteGateways
    doNotVerifyRemoteGateways: true
    remoteVirtualNetwork: {
      id: pHubVirtualNetworkId }
    remoteVirtualNetworkAddressSpace: {
      addressPrefixes: [
        remoteVirtualNetworkAddressSpace
      ]
    }
  }
}

resource networkSecurityGroup 'Microsoft.Network/networkSecurityGroups@2022-07-01' = {
  name: nsgName
  location: location
  properties: {
    securityRules: securityRules
  }
}

resource routetable 'Microsoft.Network/routeTables@2022-07-01' = {
  name: routeTableName
  location: location
  properties: {
    routes: routes
    disableBgpRoutePropagation: true
  }
}

// Outputs

output vnetId string = spokeVnet.id
output vnetName string = spokeVnet.name
output outSubnets array = spokeVnet.properties.subnets
