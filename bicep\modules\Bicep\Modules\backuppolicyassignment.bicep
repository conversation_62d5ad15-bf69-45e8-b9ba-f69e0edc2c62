targetScope = 'subscription'

@minLength(1)
@maxLength(24)
@sys.description('The name of the policy assignment. e.g. "Deny-Public-IP"')
param policyAssigmentName string

@sys.description('The display name of the policy assignment. e.g. "Deny the creation of Public IPs"')
param policyDisplayName string

@sys.description('The description of the policy assignment. e.g. "This policy denies creation of Public IPs under the assigned scope."')
param policyAssignmentDescription string

@sys.description('The policy definition ID for the policy to be assigned. e.g. "/providers/Microsoft.Authorization/policyDefinitions/9d0a794f-1444-4c96-9534-e35fc8c39c91" or "/providers/Microsoft.Management/managementgroups/alz/providers/Microsoft.Authorization/policyDefinitions/Deny-Public-IP"')
param definitionId string

@allowed([
  'Default'
  'DoNotEnforce'
])
@sys.description('The enforcement mode for the policy assignment. See https://aka.ms/EnforcementMode for more details on use.')
param enforcementMode string

@description('Parameters for the policy')
param policyParameters object

@description('Location for the policy, i.e. westeurope')
param location string

@description('When set to true, it will create a remediation task')
param createRemediationTask bool = false

@description('Array with roleassignment ids to give to the managed identity')
param roleAssignmentsIds array = []

resource policyAssignment 'Microsoft.Authorization/policyAssignments@2020-03-01' = {
  name: policyAssigmentName
  properties: {
    policyDefinitionId: definitionId
    displayName: policyDisplayName
    description: policyAssignmentDescription
    enforcementMode: enforcementMode
    parameters: policyParameters
  }
  identity: {
    type: 'SystemAssigned'
  }
  location: location
}

resource roleAssignment 'Microsoft.Authorization/roleAssignments@2022-04-01' = [for roleDefinitionResourceId in roleAssignmentsIds:  {
  scope: subscription()
  name: guid(subscription().id,policyAssignment.id,roleDefinitionResourceId)
  properties: {
    roleDefinitionId: resourceId('Microsoft.Authorization/roleDefinitions',roleDefinitionResourceId)
    principalId: policyAssignment.identity.principalId
    principalType: 'ServicePrincipal'
  }
}]



resource remediationTask 'Microsoft.PolicyInsights/remediations@2021-10-01' = if(createRemediationTask) {
  name: 'remediation'
  scope: policyAssignment
  properties: {
    policyAssignmentId: policyAssignment.id
    resourceDiscoveryMode: 'ExistingNonCompliant'
    failureThreshold: {
      percentage: 1
    }
    resourceCount: 500
    parallelDeployments: 10
  }
  dependsOn: [
    roleAssignment
  ]
}
