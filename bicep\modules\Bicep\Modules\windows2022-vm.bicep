param klantCode string
param functie string
param omgeving string
param location string = resourceGroup().location
param ipAddress string
param subnetId string
param vmSize string
param adminUsername string
@secure()
param adminPassword string
param vmZone string
param volgNummer string
param dataDisks array
param asgId string
param AcceleratedNetworking bool
param dnsServers array = []

var vmName = '${klantCode}-vm-${functie}-w${omgeving}${volgNummer}'
var vmSku = '2022-datacenter-azure-edition'

resource nic 'Microsoft.Network/networkInterfaces@2022-07-01' = {
  name: '${vmName}-nic'
  location: location
  properties: {
    ipConfigurations: [
      {
        name: 'ipconfig1'
        properties: {
          privateIPAddress: ipAddress
          privateIPAllocationMethod: 'Static'
          subnet: {
            id: subnetId
          }
          applicationSecurityGroups: [
            {
              id: asgId
              location: location

            }
          ]

        }
      }
    ]
    enableAcceleratedNetworking: AcceleratedNetworking
    dnsSettings: {
      dnsServers: dnsServers
    }
    enableIPForwarding: false
  }
}

resource vm 'Microsoft.Compute/virtualMachines@2022-08-01' = {
  dependsOn: [ nic ]
  name: vmName
  location: location
  properties: {
    hardwareProfile: {
      vmSize: vmSize
    }
    securityProfile: {
      encryptionAtHost: true
      securityType:'TrustedLaunch'
      uefiSettings:{
        secureBootEnabled:true
        vTpmEnabled:true
      }
    }
    storageProfile: {
      imageReference: {
        publisher: 'MicrosoftWindowsServer'
        offer: 'WindowsServer'
        sku: vmSku
        version: 'latest'
      }
      osDisk: {
        osType: 'Windows'
        name: '${vmName}-osdisk'
        createOption: 'FromImage'
        caching: 'ReadWrite'
        managedDisk: {
          storageAccountType: 'Premium_LRS'
        }
      }
      dataDisks: [for disk in dataDisks: {
        name: '${vmName}-datadisk${disk.datadiskNummer}'
        managedDisk: {
          storageAccountType: 'Premium_LRS' //parameter
          id: dataDisk[disk.datadiskNummer - 1].id
        }
        lun: disk.datadiskNummer - 1
        caching: disk.caching
        createOption: 'Attach'

      }]

    }
    osProfile: {
      computerName: vmName
      adminUsername: adminUsername
      adminPassword: adminPassword
    }
    networkProfile: {
      networkInterfaces: [
        {
          id: resourceId('Microsoft.Network/networkInterfaces', '${vmName}-nic')
          properties: {
            primary: true
          }
        }
      ]
    }
  }
  zones: [
    vmZone
  ]
}

resource dataDisk 'Microsoft.Compute/disks@2022-07-02' = [for disk in dataDisks: {
  name: '${vmName}-datadisk${disk.datadiskNummer}'
  zones: [ vmZone ]
  location: location
  sku: {
    name: 'Premium_LRS'
  }
  properties: {
    creationData: {
      createOption: 'Empty'
    }
    diskSizeGB: disk.datadiskSize

  }

}]
