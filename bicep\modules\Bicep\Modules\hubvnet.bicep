// Parameters

param klantCode string
param omgeving string
param location string = resourceGroup().location
param addressPrefixes array
param subnets array
param dnsServers array
param securityRules array
param workload string
param regio string

// // Variables

var vnetName = '${klantCode}-vnet-${workload}-${omgeving}-${regio}-01'
var subnetsArray = [for subnet in subnets: {
  name: '${klantCode}-sn-${subnet.function}'
  properties: {
    addressPrefix: subnet.addressPrefix
    networkSecurityGroup: {
      id: networkSecurityGroup.id
    }
    privateEndpointNetworkPolicies: 'Enabled'
  }
}]
var nsgName = '${klantCode}-nsg-${workload}-${omgeving}-${regio}-01'

// Resources

resource hubVnet 'Microsoft.Network/virtualNetworks@2022-07-01' = {
  name: vnetName
  location: location
  properties: {
    addressSpace: {
      addressPrefixes: addressPrefixes
    }
    dhcpOptions: {
      dnsServers: dnsServers
    }
    subnets: subnetsArray
  }
}

resource networkSecurityGroup 'Microsoft.Network/networkSecurityGroups@2022-07-01' = {
  name: nsgName
  location: location
  properties: {
    securityRules: securityRules
  }
}

output vnetId string = hubVnet.id
output vnetName string = hubVnet.name
output outSubnets array = hubVnet.properties.subnets
