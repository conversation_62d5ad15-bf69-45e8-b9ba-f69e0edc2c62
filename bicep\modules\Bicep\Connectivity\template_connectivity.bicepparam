using './Connectivity.bicep'

param location = 'westeurope'
param regio = 'weu'
param omgeving = 'p'
param klantCode = 'xxx'
param vnetSettings = {
  addressPrefixes: [ 'x.x.x.x/x']
          subnets: [
            {
              function: 'fortigatepublic'
              addressPrefix: 'x.x.x.x/x'
            }
            {
              function: 'fortigateprivate'
              addressPrefix: 'x.x.x.x/x'
            }
          ]
          dnsServers: ['x.x.x.x','x.x.x.x']
          
        }
param peerings = [
    {
     name: 'management' 
      id: '/subscriptions/xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxxresourceGroups/xxx-rg-management-p-network-weu/providers/Microsoft.Network/virtualNetworks/xxx-vnet-management-p-weu-xx'
      remoteVirtualNetworkAddressSpace: 'xx.xx.xx.xx/xx'
    }
    {
      name: 'identity' 
      id: '/subscriptions/xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx/resourceGroups/xxx-rg-identity-p-network-weu/providers/Microsoft.Network/virtualNetworks/xxx-vnet-identity-p-weu-xx'
      remoteVirtualNetworkAddressSpace: 'xx.xx.xx.xx/xx'
    }
    ]

    param securityRules = [
      {
          name: 'AllowAllInbound'
          properties:{
          protocol: '*'
          sourcePortRange: '*'
          destinationPortRange: '*'
          destinationAddressPrefix: '*'
          sourceAddressPrefix: '*'
          access: 'Allow'
          priority: 1000
          direction: 'Inbound'
          sourcePortRanges: ''
        }
      }
      {
          name: 'DenyAllInbound'
          properties:{
          protocol: '*'
          sourcePortRange: '*'
          destinationPortRange: '*'
          sourceAddressPrefix: '*'
          destinationAddressPrefix: '*'
          access: 'Deny'
          priority: 4096
          direction: 'Inbound'
        }
      }
      {
          name: 'AllowAllOutbound'
          properties:{
          protocol: '*'
          sourcePortRange: '*'
          destinationPortRange: '*'
          sourceAddressPrefix: '*'
          destinationAddressPrefix: '*'
          access: 'Allow'
          priority: 1000
          direction: 'outbound'
        }
      }
      ]
  