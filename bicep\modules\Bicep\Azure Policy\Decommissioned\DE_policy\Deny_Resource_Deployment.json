{"properties": {"displayName": "Disallowed Resource Deployment", "policyType": "Custom", "mode": "All", "description": "This policy prevents the deployment of any resources in the decommissioned management group.", "metadata": {"version": "1.0.0", "category": "General"}, "parameters": {"listOfResourceTypesAllowed": {"type": "Array", "defaultValue": [], "metadata": {"description": "An empty list to prevent any resource deployment.", "displayName": "Allowed resource types", "strongType": "resourceTypes"}}}, "policyRule": {"if": {"not": {"field": "type", "in": "[parameters('listOfResourceTypesAllowed')]"}}, "then": {"effect": "deny"}}}}