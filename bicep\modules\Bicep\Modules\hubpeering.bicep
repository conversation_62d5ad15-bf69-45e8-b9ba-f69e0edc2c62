param vnetName string
param name string
param allowVirtualNetworkAccess bool 
param allowForwardedTraffic bool
param pAllowGatewayTransit bool
param pUseRemoteGateways bool
param doNotVerifyRemoteGateways bool
param pVirtualNetworkId string
param remoteVirtualNetworkAddressSpace string

resource localVirtualNetwork 'Microsoft.Network/virtualNetworks@2022-09-01' existing = {
  name: vnetName
}

resource hubVirtualNetworkPeeringManagement 'Microsoft.Network/virtualNetworks/virtualNetworkPeerings@2023-04-01' = {
   name: name
   parent: localVirtualNetwork
   properties: {
     allowVirtualNetworkAccess: allowVirtualNetworkAccess 
     allowForwardedTraffic: allowForwardedTraffic
     allowGatewayTransit: pAllowGatewayTransit
     useRemoteGateways: pUseRemoteGateways
     doNotVerifyRemoteGateways: doNotVerifyRemoteGateways
     remoteVirtualNetwork: {
      id: pVirtualNetworkId }
     remoteVirtualNetworkAddressSpace: {
      addressPrefixes: [
        remoteVirtualNetworkAddressSpace
      ]
  
    }
   }
 }
