targetScope = 'managementGroup'

//Parameters
param policySource string 
param policyCategory string 
param assignmentEnforcementMode string 
param WLSEC02Id string = '/providers/Microsoft.Authorization/policySetDefinitions/be7a78aa-3e10-4153-a5fd-8c6506dbc821'

// Variables
var WLGOV01initiativeName = 'Enforce enhanced recovery and backup policies'
var WLGOV01assignmentName = 'WL.GOV.01.v01'
var WLSEC01initiativeName = 'Enforce recommended guardrails for Azure Key Vault'
var WLSEC01assignmentName = 'WL.SEC.01.v01'
var WLCOMP01initiativeName = 'Configure periodic checking for missing system updates'
var WLCOMP01assignmentName = 'PF.COMP.01.v01'
var worklinitiativeName = 'Workloads'
var worklassignmentName = 'Workloads'

// Policy deffenition sets (initiative)

// RESOURCES

resource WLGOV01 'Microsoft.Authorization/policySetDefinitions@2023-04-01' = {
  name: WLGOV01initiativeName
  properties: {
    policyType: 'Custom'
    displayName: 'PF.GOV.01.v01 Enforce enhanced recovery and backup policies'
    description: '${WLGOV01initiativeName} via ${policySource}'
    metadata: {
      category: policyCategory
      source: policySource
      version: '0.0.1'
    }
    policyDefinitions: [
      {
        // WL.GOV.01.v01 [Preview]: Immutability must be enabled for backup vaults
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/2514263b-bc0d-4b06-ac3e-f262c0979018'
        policyDefinitionReferenceId: 'WL.GOV.01.v01 [Preview]: Immutability must be enabled for backup vaults'
        parameters: {}
      }
      {
        // WL.GOV.01.v01 [Preview]: Immutability must be enabled for Recovery Services vaults
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/d6f6f560-14b7-49a4-9fc8-d2c3a9807868'
        policyDefinitionReferenceId: 'WL.GOV.01.v01 [Preview]: Immutability must be enabled for Recovery Services vaults'
        parameters: {}
      }
      {
        // WL.GOV.01.v01 [Preview]: Multi-User Authorization (MUA) must be enabled for Backup Vaults.
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/c58e083e-7982-4e24-afdc-be14d312389e'
        policyDefinitionReferenceId: 'WL.GOV.01.v01 [Preview]: Multi-User Authorization (MUA) must be enabled for Backup Vaults'
        parameters: {}
      }
      {
        // WL.GOV.01.v01 [Preview]: Multi-User Authorization (MUA) must be enabled for Recovery Services Vaults.
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/c7031eab-0fc0-4cd9-acd0-4497bd66d91a'
        policyDefinitionReferenceId: 'WL.GOV.01.v01 [Preview]: Multi-User Authorization (MUA) must be enabled for Recovery Services Vaults'
        parameters: {}
      }
      {
        // WL.GOV.01.v01 [Preview]: Soft delete must be enabled for Recovery Services Vaults
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/31b8092a-36b8-434b-9af7-5ec844364148'
        policyDefinitionReferenceId: 'WL.GOV.01.v01 [Preview]: Soft delete must be enabled for Recovery Services Vaults'
        parameters: {}
      }
      {
        // WL.GOV.01.v01 [Preview]: Soft delete should be enabled for Backup Vaults
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/9798d31d-6028-4dee-8643-46102185c016'
        policyDefinitionReferenceId: 'WL.GOV.01.v01 [Preview]: Soft delete should be enabled for Backup Vaults'
        parameters: {}
      }
    ]
  }
}

resource WLSEC01 'Microsoft.Authorization/policySetDefinitions@2023-04-01' = {
  name: WLSEC01initiativeName
  properties: {
    policyType: 'Custom'
    displayName: 'PF.SEC.01.v01 Enforce recommended guardrails for Azure Key Vault'
    description: '${WLSEC01initiativeName} via ${policySource}'
    metadata: {
      category: policyCategory
      source: policySource
      version: '0.0.1'
    }
    policyDefinitions: [
      {
        // WL.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM keys should have an expiration date
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/1d478a74-21ba-4b9f-9d8f-8e6fced0eec5'
        policyDefinitionReferenceId: 'WL.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM keys should have an expiration date'
        parameters: {}
      }
      {
        // WL.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM Keys should have more than the specified number of days before expiration
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/ad27588c-0198-4c84-81ef-08efd0274653'
        policyDefinitionReferenceId: 'WL.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM Keys should have more than the specified number of days before expiration'
        parameters: {}
      }
      {
        // WL.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM keys using elliptic curve cryptography should have the specified curve names
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/e58fd0c1-feac-4d12-92db-0a7e9421f53e'
        policyDefinitionReferenceId: 'WL.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM keys using elliptic curve cryptography should have the specified curve names'
        parameters: {}
      }
      {
        // WL.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM keys using RSA cryptography should have a specified minimum key size
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/86810a98-8e91-4a44-8386-ec66d0de5d57'
        policyDefinitionReferenceId: 'WL.SEC.01.v01 [Preview]: Azure Key Vault Managed HSM keys using RSA cryptography should have a specified minimum key size'
        parameters: {}
      }
      {
        // WL.SEC.01.v01 Azure Key Vault Managed HSM should have purge protection enabled
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/c39ba22d-4428-4149-b981-70acb31fc383'
        policyDefinitionReferenceId: 'WL.SEC.01.v01 Azure Key Vault Managed HSM should have purge protection enabled'
        parameters: {}
      }
      {
        // WL.SEC.01.v01 Azure Key Vault should have firewall enabled
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/55615ac9-af46-4a59-874e-391cc3dfb490'
        policyDefinitionReferenceId: 'WL.SEC.01.v01 Azure Key Vault should have firewall enabled'
        parameters: {}
      }
      {
        // WL.SEC.01.v01 Azure Key Vault should use RBAC permission model
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/12d4fa5e-1f9f-4c21-97a9-b99b3c6611b5'
        policyDefinitionReferenceId: 'WL.SEC.01.v01 Azure Key Vault should use RBAC permission model'
        parameters: {}
      }
      {
        // WL.SEC.01.v01 Certificates should be issued by the specified integrated certificate authority
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/8e826246-c976-48f6-b03e-619bb92b3d82'
        policyDefinitionReferenceId: 'WL.SEC.01.v01 Certificates should be issued by the specified integrated certificate authority'
        parameters: {}
      }/*
      {
        // WL.SEC.01.v01 Certificates should be issued by the specified non-integrated certificate authority
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/a22f4a40-01d3-4c7d-8071-da157eeff341'
        policyDefinitionReferenceId: 'WL.SEC.01.v01 Certificates should be issued by the specified non-integrated certificate authority'
        parameters: {}
      }//caCommonname missing 
      {
        // WL.SEC.01.v01 Certificates should have the specified lifetime action triggers
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/12ef42cb-9903-4e39-9c26-422d29570417'
        policyDefinitionReferenceId: 'WL.SEC.01.v01 Certificates should have the specified lifetime action triggers'
        parameters: {}
      }*/  //is missing the parameter(s) 'maximumPercentageLife,minimumDaysBeforeExpiry' as defined in the policy definition
    ]
  }
}

resource worklinitiative 'Microsoft.Authorization/policySetDefinitions@2023-04-01' = {
  name: worklinitiativeName
  properties: {
    policyType: 'Custom'
    displayName: worklinitiativeName
    description: '${worklinitiativeName} via ${policySource}'
    metadata: {
      category: policyCategory
      source: policySource
      version: '0.0.1'
    }
    policyDefinitions: [
      {
        // WL.SEC.03.v01 Secure transfer to storage accounts should be enabled
        policyDefinitionId: 'providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9'
        policyDefinitionReferenceId: 'WL.SEC.01.v01 Secure transfer to storage accounts should be enabled'
        parameters: {}
      }
      {
        // WL.SEC.04.v01 Web Application Firewall (WAF) should be enabled for Application Gateway
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/564feb30-bf6a-4854-b4bb-0d2d2d1e6c66'
        policyDefinitionReferenceId: 'WL.SEC.04.v01 Web Application Firewall (WAF) should be enabled for Application Gateway'
        parameters: {}
      }
    ]
  }
}

resource WLCOMP01 'Microsoft.Authorization/policySetDefinitions@2023-04-01' = {
  name: WLCOMP01initiativeName
  properties: {
    policyType: 'Custom'
    displayName: 'PF.COMP.01.v01 Configure periodic checking for missing system updates'
    description: '${WLCOMP01initiativeName} via ${policySource}'
    metadata: {
      category: policyCategory
      source: policySource
      version: '0.0.1'
    }
    policyDefinitions: [
      {
        // WL.COMP.01.v01 Configure periodic checking for missing system updates on azure virtual machines (Windows)
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/59efceea-0c96-497e-a4a1-4eb2290dac15'
        policyDefinitionReferenceId: 'WL.COMP.01.v01 Configure periodic checking for missing system updates on azure virtual machines (Windows)'
        parameters: {
          osType: {
            value: 'Windows'
          }    
        }
      }
      {
        // WL.COMP.01.v01 Configure periodic checking for missing system updates on azure virtual machines (Linux)
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/59efceea-0c96-497e-a4a1-4eb2290dac15'
        policyDefinitionReferenceId: 'WL.COMP.01.v01 Configure periodic checking for missing system updates on azure virtual machines (Linux)'
        parameters: {
          osType: {
            value: 'Linux'
          }    
        }
      }
      {
        // WL.COMP.01.v01 Configure periodic checking for missing system updates on azure Arc-enabled servers (Windows)
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/bfea026e-043f-4ff4-9d1b-bf301ca7ff46'
        policyDefinitionReferenceId: 'WL.COMP.01.v01 Configure periodic checking for missing system updates on azure Arc-enabled servers (Windows)'
        parameters: {
          osType: {
            value: 'Windows'
          }    
        }
      }
      {
        // WL.COMP.01.v01 Configure periodic checking for missing system updates on azure Arc-enabled servers (Linux)
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/bfea026e-043f-4ff4-9d1b-bf301ca7ff46'
        policyDefinitionReferenceId: 'WL.COMP.01.v01 Configure periodic checking for missing system updates on azure Arc-enabled servers (Linux)'
        parameters: {
          osType: {
            value: 'Linux'
          }    
        }
      }
    ]
  }
}

// ASSIGNMENTS

resource WLGOV01assignment 'Microsoft.Authorization/policyAssignments@2023-04-01' = {
  name: WLGOV01assignmentName
  location: 'west europe'
  identity:{
   type:'SystemAssigned'
  }
  properties: {
    displayName: WLGOV01assignmentName
    description: '${WLGOV01assignmentName} via ${policySource}'
    enforcementMode: assignmentEnforcementMode
    metadata: {
      source: policySource
      version: '0.0.1'
    }
    policyDefinitionId: WLGOV01.id      
  }
}

resource WLSEC01assignment 'Microsoft.Authorization/policyAssignments@2023-04-01' = {
  name: WLSEC01assignmentName
  location: 'west europe'
  identity:{
   type:'SystemAssigned'
  }
  properties: {
    displayName: WLSEC01assignmentName
    description: '${WLSEC01assignmentName} via ${policySource}'
    enforcementMode: assignmentEnforcementMode
    metadata: {
      source: policySource
      version: '0.0.1'
    }
    policyDefinitionId: WLSEC01.id      
  }
}

resource worklassignment 'Microsoft.Authorization/policyAssignments@2023-04-01' = {
  name: worklassignmentName
  location: 'west europe'
  identity:{
   type:'SystemAssigned'
  }
  properties: {
    displayName: worklassignmentName
    description: '${worklassignmentName} via ${policySource}'
    enforcementMode: assignmentEnforcementMode
    metadata: {
      source: policySource
      version: '0.0.1'
    }
    policyDefinitionId: worklinitiative.id      
  }
}

resource WLSEC02 'Microsoft.Authorization/policyAssignments@2021-06-01' = {
  name: 'WLSEC02v01' //audit
  properties: {
    displayName: 'WL.SEC.02.v01 [Preview]: Windows machines should meet requirements for the Azure compute security baseline'
    policyDefinitionId: WLSEC02Id
    enforcementMode: assignmentEnforcementMode
  }
}

resource WLCOMP01assignment 'Microsoft.Authorization/policyAssignments@2023-04-01' = {
  name: WLCOMP01assignmentName
  location: 'west europe'
  identity:{
   type:'SystemAssigned'
  }
  properties: {
    displayName: WLCOMP01assignmentName
    description: '${WLCOMP01assignmentName} via ${policySource}'
    enforcementMode: assignmentEnforcementMode
    metadata: {
      source: policySource
      version: '0.0.1'
    }
    policyDefinitionId: WLCOMP01.id      
  }
}
