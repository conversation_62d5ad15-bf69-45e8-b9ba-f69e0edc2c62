{"if": {"allOf": [{"field": "type", "equals": "Microsoft.Resources/subscriptions"}]}, "then": {"effect": "DeployIfNotExists", "details": {"type": "Microsoft.Security/pricings", "name": "VirtualMachines", "deploymentScope": "subscription", "existenceScope": "subscription", "roleDefinitionIds": ["/providers/Microsoft.Authorization/roleDefinitions/fb1c8493-542b-48eb-b624-b4c8fea62acd"], "existenceCondition": {"field": "Microsoft.Security/pricings/pricingTier", "equals": "Standard"}, "deployment": {"location": "westeurope", "properties": {"mode": "incremental", "parameters": {}, "template": {"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {}, "variables": {}, "resources": [{"type": "Microsoft.Security/pricings", "apiVersion": "2022-03-01", "name": "VirtualMachines", "properties": {"pricingTier": "Standard", "subPlan": "P1"}}, {"type": "Microsoft.Security/pricings", "apiVersion": "2022-03-01", "name": "arm", "properties": {"pricingTier": "standard"}}], "outputs": {}}}}}}}