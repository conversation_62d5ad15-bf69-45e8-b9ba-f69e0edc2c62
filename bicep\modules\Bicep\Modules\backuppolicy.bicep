targetScope = 'subscription'
param klantCode string
param workload string
param location string 
param backupPolicyV2Id string //recoveryvault.outputs.backupPolicyV2Id
param exclusionTag string
param existingPolicyAssignment string




module backuppolicyassignment '../Modules/backuppolicyassignment.bicep' = if (existingPolicyAssignment == null) {
  name: 'backuppolicyassignment'
  params: {
    definitionId: '/providers/Microsoft.Authorization/policyDefinitions/09ce66bc-1220-4153-8104-e3f51c936913'
    enforcementMode: 'Default'
    policyAssigmentName: '${klantCode}-VmBackup'
    policyAssignmentDescription: 'Assigns the default vm backup policy from ${klantCode} to all vm resources without the specified tag: ${exclusionTag}'
    policyDisplayName: '${workload}-VmBackupPolicy'
    policyParameters: {
      vaultLocation: {
        value: location
      }
      backupPolicyId: {
        value: backupPolicyV2Id
      }
      exclusionTagName: {
        value: exclusionTag
      }
      exclusionTagValue: {
        value: [true]
      }
      effect: {
        value: 'DeployIfNotExists'
      }
    }
    location: location
    createRemediationTask:true
    roleAssignmentsIds: ['9980e02c-c2be-4d73-94e8-173b1dc7cf3c','5e467623-bb1f-42f4-a55d-6e525e11384b']  //Virtual Machine Contributor, Backup Contributor

  }
}
