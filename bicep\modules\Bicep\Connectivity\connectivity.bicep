targetScope = 'subscription'

param location string = 'westeurope'
param regio string = 'weu'
param omgeving string
param klantCode string
param vnetSettings object
param peerings array

param allowVirtualNetworkAccess bool = true
param allowForwardedTraffic bool = true

@description('True for hub and false for spoke.')
param pAllowGatewayTransit bool = false

@description('set to true if the hub has gatewaysubnet and on-premise traffic from or to the spoke is needed.')
param pUseRemoteGateways bool = false

param doNotVerifyRemoteGateways bool = true

param securityRules array

var workload = 'connectivity' // Microsoft Entra Domain Services(Azure Active Domain services)
var rsgfortigateName = '${klantCode}-rg-${workload}-${omgeving}-fortigate-${regio}'
var rsgnetworkName = '${klantCode}-rg-${workload}-${omgeving}-network-${regio}'
var vulnerabilityRsgName = '${klantCode}-rg-vulnerabilitymanagement-p'

resource fortigate_rg 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: rsgfortigateName
  location: location
}

resource network_rg 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: rsgnetworkName
  location: location
}

resource vulnerability_rg 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: vulnerabilityRsgName
  location: location
}

module hubVnet '../Modules/hubvnet.bicep' = {
  scope: resourceGroup(rsgnetworkName)
  dependsOn:[
    network_rg
  ]
  name: 'hubVnet'
  params:{
    addressPrefixes: vnetSettings.addressPrefixes
    dnsServers: vnetSettings.dnsServers
    klantCode: klantCode
    omgeving: omgeving
    securityRules: securityRules
    subnets:  vnetSettings.subnets
    workload: workload
    location: location
    regio: regio
  }
}
module hubpeering '../Modules/hubpeering.bicep' = [for (peering, p) in peerings: {
  dependsOn: [
    hubVnet
  ]
  scope:resourceGroup(rsgnetworkName)
  name: 'hub-to-${peering.name}'
  params:{
    name:'hub-to-${peering.name}'
    pVirtualNetworkId: peering.id
    vnetName: hubVnet.outputs.vnetName 
    remoteVirtualNetworkAddressSpace:peering.remoteVirtualNetworkAddressSpace
    allowForwardedTraffic:allowForwardedTraffic
    allowVirtualNetworkAccess:allowVirtualNetworkAccess
    doNotVerifyRemoteGateways:doNotVerifyRemoteGateways
    pAllowGatewayTransit: pAllowGatewayTransit
    pUseRemoteGateways: pUseRemoteGateways 
  }
}]
