
param(
    $managementGroupId,
    $location= 'westeurope'
)

connect-azaccount

Write-Host "Starting script assign-lighthousePolicy"
$scriptPath = split-path $MyInvocation.MyCommand.Path -Parent

Write-Host "Starting policy definition deployment"
# Policy definition beschikbaar maken
New-AzManagementGroupDeployment -Name "LighthousePolicyDefinition_$managementGroupId" `
    -Location $location `
    -ManagementGroupId $managementGroupId `
    -TemplateFile $scriptPath\policydefinition\deployLighthouseOnSubscriptionManagementGroup.json `
    -TemplateParameterFile $scriptPath\policydefinition\deployLighthouseOnSubscriptionManagementGroup-parameters.json `
    -verbose

# Policy Assignment doen

$definition = Get-AzPolicyDefinition -Name 'Enable-Azure-Lighthouse' -ManagementGroupName $managementGroupId

$policyAssignment = New-AzPolicyAssignment -Name 'LightHousePermissions' -Scope "/providers/Microsoft.Management/managementGroups/$managementGroupId" `
    -PolicyDefinition $definition -IdentityType SystemAssigned -Location $location


$scope = "/providers/Microsoft.Management/managementGroups/$managementGroupId"

# Wait for indentity to be available
while($true){
    try {
       $checkRoleAssignment = Get-AzRoleAssignment -ObjectId $policyAssignment.Identity.PrincipalId -Scope $scope

       Write-Host 'role found'
       break
    }
    catch {
        write-host 'sleeping for 5 seconds'
        start-sleep -Seconds 5
    }
}



if($checkRoleAssignment.RoleDefinitionName -contains 'Owner'){
    Write-Host "Owner role is allready present."
}
else {
    Write-Host "Setting owner role for {$policyAssignment.Identity.PrincipalId}"
    while($true){
        try {
            New-AzRoleAssignment -ObjectId $policyAssignment.Identity.PrincipalId `
            -RoleDefinitionName owner `
            -Scope $scope -Verbose -ErrorAction Stop

            break
        }
        catch {
            write-host 'Sleeping 5 seconds'
            Start-Sleep -Seconds 5
        }
    }

}

if($checkRoleAssignment.RoleDefinitionName -contains 'Reader'){
    Write-Host "Role is allready present."
}
else {
    Write-Host "Setting reader role for {$policyAssignment.Identity.PrincipalId}"
    New-AzRoleAssignment -ObjectId $policyAssignment.Identity.PrincipalId `
        -RoleDefinitionName reader `
        -Scope $scope -Verbose
}



Start-AzPolicyRemediation -Name 'remediationTask' -PolicyAssignmentId $policyAssignment.PolicyAssignmentId -Scope $scope

disconnect-azaccount