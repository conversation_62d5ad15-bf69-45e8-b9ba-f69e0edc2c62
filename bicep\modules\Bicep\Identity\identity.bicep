targetScope = 'subscription'

param location string
param regio string
param omgeving string
param klantCode string
param childAdMembersAddressPrefixes array
param managementAddressPrefixes array
param vnetSettings object
param hubData object
param remoteVirtualNetworkAddressSpace string

param aaddsDomainConfigurationType string
param aaddsDomainSecuritySettings object
param aaddsFilteredSync string
param aaddsLdapsSettings object
param aaddsNotificationSettings object
param aaddsSku string
param aaddsSyncScope string
param DomainName string
param scomgatewayAddressPrefix string
param workload string 

var rsgSpokeName = '${klantCode}-rg-${workload}-${omgeving}-network-${regio}'
var rsgaaddsName = '${klantCode}-rg-${workload}-${omgeving}-aadds-${regio}'
var vulnerabilityRsgName = '${klantCode}-rg-vulnerabilitymanagement-p'
var addressPrefixesString = join(childAdMembersAddressPrefixes, ',') // Join array elements with a comma

var addsMember_tcp_ports = [
  '135'
  '464'
  '389'
  '636'
  '3268'
  '3269'
  '53'
  '88'
  '445'
  '49152-65535'
  '9389'
]
var addsMember_udp_ports = [
  '123'
  '464'
  '53'
  '88'
  '389'
]

var securityRules = [
  {
    name: 'Allow_Inbound_Members_to_AADDS_tcp'
    properties: {
      protocol: 'TCP'
      sourcePortRange: '*'
      destinationPortRanges: addsMember_tcp_ports
      sourceAddressPrefixes: childAdMembersAddressPrefixes
      destinationAddressPrefixes: vnetSettings.addressPrefixes
      access: 'Allow'
      priority: 1100
      direction: 'Inbound'
      sourcePortRanges: []
    }
  }
  {
    name: 'Allow_Inbound_Members_to_AADDS_udp'
    properties: {
      protocol: 'UDP'
      sourcePortRange: '*'
      destinationPortRanges: addsMember_udp_ports
      sourceAddressPrefixes: childAdMembersAddressPrefixes
      destinationAddressPrefixes: vnetSettings.addressPrefixes
      access: 'Allow'
      priority: 1120
      direction: 'Inbound'
      sourcePortRanges: []
    }
  }
  {
    name: 'Allow_Inbound_Management'
    properties: {
      protocol: 'TCP'
      sourcePortRange: '*'
      destinationPortRange: '3389'
      sourceAddressPrefixes: managementAddressPrefixes
      destinationAddressPrefixes: vnetSettings.addressPrefixes
      access: 'Allow'
      priority: 1300
      direction: 'Inbound'
      sourcePortRanges: []
    }
  }
  {
    name: 'Allow_inbound_to_Scom_Gateway'
    properties: {
      protocol: 'TCP'
      sourcePortRange: '*'
      destinationPortRange: 5723
      destinationAddressPrefixes: vnetSettings.addressPrefixes
      sourceAddressPrefix: scomgatewayAddressPrefix
      access: 'Allow'
      priority: 1400
      direction: 'Inbound'
      sourcePortRanges: []
    }
  }
  {
    name: 'Allow_Inbound_AzureActiveDirectoryDomainServices'
    properties: {
      protocol: 'TCP'
      sourcePortRange: '*'
      destinationPortRange: '5986'
      sourceAddressPrefix: 'AzureActiveDirectoryDomainServices'
      destinationAddressPrefix: '*'
      access: 'Allow'
      priority: 1500
      direction: 'Inbound'
      sourcePortRanges: []
    }
  }
]

var routeTableName = '${klantCode}-rt-${workload}-${omgeving}-${regio}-01'
var routeName = '${klantCode}-rt-${workload}-${omgeving}-${regio}-02'
var routes = [
  {
    name: routeName
    properties: {
      addressPrefix: addressPrefixesString
      nextHopIpAddress: hubData.firewallIp
      nextHopType: 'VirtualAppliance'
    }
  }
  {
    name: routeTableName
    properties: {
      addressPrefix: '0.0.0.0/0'
      nextHopType: 'Internet'
    }
  }
]  

resource aadds_rg 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: rsgaaddsName
  location: location
}

resource aadds_spoke_rg 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: rsgSpokeName
  location: location
}

resource vulnerability_rg 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: vulnerabilityRsgName
  location: location
}

module spokeVnetAadds '../Modules/spokevnet.bicep' = {
  scope: resourceGroup(aadds_spoke_rg.name)
  name: 'vnet'
  params: {
    klantCode: klantCode
    omgeving: omgeving
    addressPrefixes: vnetSettings.addressPrefixes
    dnsServers: vnetSettings.dnsServers
    subnets: vnetSettings.subnets
    location: aadds_spoke_rg.location
    securityRules: securityRules
    routes: routes
    routeTableName: routeTableName
    workload: workload
    regio: regio
    remoteVirtualNetworkAddressSpace: remoteVirtualNetworkAddressSpace
    pHubVirtualNetworkId: hubData.networkId
  }
}
module aaddsmodule '../Modules/aadds.bicep' = {
  scope: aadds_rg
  name: 'aadds'
  params:{
    location: location
    aaddsDomainConfigurationType: aaddsDomainConfigurationType
    aaddsDomainName: DomainName
    aaddsDomainSecuritySettings: aaddsDomainSecuritySettings
    aaddsFilteredSync: aaddsFilteredSync
    aaddsLdapsSettings: aaddsLdapsSettings
    aaddsNotificationSettings: aaddsNotificationSettings
    aaddsSku: aaddsSku
    aaddsSyncScope: aaddsSyncScope
    aaddsReplicaSetsSubnet: spokeVnetAadds.outputs.outSubnets[0].id
  }
}
