# Werkinstructie – Azure Policy Baseline

### Doel
Azure Policy Baseline

### Inhoud
- Inleiding
- Termen
- Policy naamgeving
- Structu<PERSON> van de Bicep bestanden
- Azure Policy Deployment
- Troubleshooting

## Inleiding
Azure Policy helpt bij het monitoren en handhaven van compliance binnen de Azure-omgeving. Deze baseline omvat een reeks policies die ervoor zorgen dat Azure Lighthouse, Defender for Servers, governance, en compute resources continu worden gemonitord en, indien nodig, automatisch worden geïmplementeerd als ze nog niet aanwezig zijn.

De werkinstructie bevat een uitleg over de opbouw van het script, hoe het correct moet worden deployed in een omgeving, en het proces dat gevolgd moet worden om het script aan te passen wanneer nodig.

## Termen
| Term                      | Uitleg                                                                                                                                                                       |
|---------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| targetScope               | In Azure Policy verwijst targetScope naar het niveau waarop een Policy Definition of initiative van toepassing is.                                                            |
| Policy Definition          | Een Policy Definition specificeert één specifieke regel die nageleefd moet worden binnen de Azure-omgeving. Deze regel controleert of een resource voldoet aan bepaalde voorwaarden, en kan indien nodig afdwingen dat acties worden ondernomen om non-compliance te voorkomen of te corrigeren. |
| Policy Initiative         | Een Policy Initiative is een verzameling van meerdere Policy Definitions. Het wordt gebruikt om een groep policies samen te bundelen die een gemeenschappelijk doel dienen. |
| Policy Assignment         | Een Policy Assignment is het proces waarbij een Policy Definition of een Policy Initiative wordt toegewezen aan een specifieke scope, zoals een management group, subscription, resource group, of individuele resource. |
| policySource              | policySource verwijst naar de bron van de Azure Policy, zoals de versie en het gedetailleerde ontwerp (low-level design) van Azure policy basline.                        |
| policyCategory            | Met de code wordt een nieuwe categorie genaamd "RAM Infotechnology" aangemaakt, zodat onze baseline policies eenvoudig gefilterd kunnen worden. Deze categorie wordt toegewezen via het policyCategory-veld, waarmee de policies georganiseerd en beter vindbaar zijn binnen de Azure Policy-omgeving. |
| assignmentEnforcementMode | assignmentEnforcementMode bepaalt hoe een Policy Initiative wordt afgedwongen. Standaard staat deze instelling op "Default", wat betekent dat de acties van elke individuele Policy Definition binnen het initiatief (zoals Audit, Deny, of DeployIfNotExists) automatisch worden uitgevoerd. |
| policyDefinitionReferenceId| policyDefinitionReferenceId is een unieke identifier die zowel het nummer als de naam van de policy bevat, zoals gedefinieerd in het low-level design (LLD). Deze referentie maakt het gemakkelijk om specifieke Policy Definitions binnen een Policy Initiative te identificeren en te beheren. |

## Policy naamgeving
Er wordt gebruik gemaakt van de onderstaande naamgeving. Omdat de policy lijst dynamisch is, zal er voor iedere category opnieuw begonnen worden met 1. Zie onderstaande voorbeeld:

### Voorbeeld Policy naamgeving:
| Customer_Code                                                       |
|---------------------------------------------------------------------|
| CC.GOV.01 Lighthouse RAM Infotechnology                              |
| CC.GOV.02 RAM Tagging - Team Accountable for day to day ops        |
| CC.SEC.01 Deploy Microsoft Defender for Server Plan 1               |
| CC.IAM.01 Accounts with read permissions on Azure resources should be MFA enabled |
| CC.IAM.02 Accounts with write permissions on Azure resources should be MFA enabled |



### Naamgeving policy per managementgroep:
| Managementgroep | Policy naamgeving     |
|------------------|-----------------------|
| Customer Code    | CC.category.num.v       |
| Platform         | PF.category.num.v       |
| Connectivity      | CO.category.num.v       |
| Identity         | ID.category.num.v       |
| Management       | MG.category.num.v       |
| Workloads        | CC.category.num.v       |
| Corp             | CO.category.num.v       |
| Online           | ON.category.num.v       |
| Sandbox          | SA.category.num.v       |
| Decommissioned    | DE.category.num.v       |

### Categorie shortcode:
| Categorie                   | Short code |
|----------------------------|------------|
| Governance                 | GOV        |
| Security Management        | SEC        |
| Identity & Access Management| IAM        |
| Computer Operations        | COMP       |
| Cost Optimization (billing) | COST       |

## Structuur van de Bicep bestanden
Omdat het niet mogelijk en ook niet praktisch is om alle Bicep-code voor afzonderlijke managementgroepen in één bestand te bundelen, is er voor elke managementgroep een apart bestand aangemaakt met de bijbehorende policies. Deze bestanden worden vanuit één overkoepelend deployment-bestand naar de omgeving uitgerold. Dit biedt de flexibiliteit om bijvoorbeeld al goedgekeurde policies, zoals die op de Customer Code-laag, te enforcen, terwijl andere policies nog op DoNotEnforce kunnen blijven staan totdat ze volledig zijn getest en goedgekeurd.

Daarnaast zorgt deze aanpak ervoor dat alle policies dezelfde categorie en metadata krijgen, wat bijdraagt aan een consistente en overzichtelijke structuur.


## Troubleshooting

1. Soms kunt u een foutmelding krijgen tijdens het uitvoeren van uw code. Dit kan veroorzaakt worden door het gebruik van een ongeldige API. Lees de foutmelding aandachtig om te controleren welke API-versies worden ondersteund voor de uitvoering.

2. Sommige policies vereisen dat specifieke resource providers zijn geïnstalleerd in een subscriptie voordat de policy kan worden toegepast of deployed. Een goed voorbeeld hiervan is de Lighthouse-policy. Om deze policy succesvol te kunnen deployen, moeten de volgende providers geïnstalleerd zijn:
   - Microsoft.ManagedServices
   - Microsoft.Management

## Azure Policy Deployment
```bash
az deployment mg create --template-file .\naam_van_de_main_file.bicep --location westeurope --management-group-id "een management groep id" --what-if


