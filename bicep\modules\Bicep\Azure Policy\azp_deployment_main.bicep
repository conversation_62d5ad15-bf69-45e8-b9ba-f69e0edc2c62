targetScope = 'managementGroup'

param managementGroupId1 string = '<hier je management ID>' // Naam van de management group Customer_code 
param managementGroupId2 string = '<hier je management ID>' // Naam van de management group Platform 
param managementGroupId6 string = '<hier je management ID>' // Naam van de management group Workloads 
param managementGroupId9 string = '<hier je management ID>' // Naam van de management group Decomissioned 
param managementGroupId10 string = '<hier je management ID>' // Naam van de management group Sandbox 
param policySource string = '0.0.1 Low Level Design - Azure Policy versie 0.0.1'
param policyCategory string = 'RAM Infotechnology'
param assignmentEnforcementMode string = 'DoNotEnforce'

module ccodeAssignment './custcode_azpolicy.bicep' = {
  name: 'ccodeAssignment'
  scope: managementGroup(managementGroupId1)
  params: {
    policySource: policySource
    policyCategory: policyCategory
    assignmentEnforcementMode: assignmentEnforcementMode
  }
}

module platfassignment './platform_azpolicy.bicep' = {
  name: 'platfAssignment'
  scope: managementGroup(managementGroupId2)
  params: {
    policySource: policySource
    policyCategory: policyCategory
    assignmentEnforcementMode: assignmentEnforcementMode
  }
}

module worklassignment './workloads_azpolicy.bicep' = {
  name: 'worklAssignment'
  scope: managementGroup(managementGroupId6)
  params: {
    policySource: policySource
    policyCategory: policyCategory
    assignmentEnforcementMode: assignmentEnforcementMode
  }
}

module decomassignment './decomissioned_azpolicy.bicep' = {
  name: 'decomAssignment'
  scope: managementGroup(managementGroupId9)
  params: {
    policySource: policySource
    policyCategory: policyCategory
    assignmentEnforcementMode: assignmentEnforcementMode
  }
}

module sandbassignment './sandbox_azpolicy.bicep' = {
  name: 'sandbAssignment'
  scope: managementGroup(managementGroupId10)
  params: {
    policySource: policySource
    policyCategory: policyCategory
    assignmentEnforcementMode: assignmentEnforcementMode
  }
}
