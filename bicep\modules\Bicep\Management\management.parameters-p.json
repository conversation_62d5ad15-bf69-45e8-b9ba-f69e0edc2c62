{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"omgeving": {"value": "p"}, "vnetSettings": {"value": {"addressPrefixes": ["***********/27"], "subnets": [{"function": "management", "addressPrefix": "***********/27"}], "dnsServers": ["***********", "***********"]}}, "hubData": {"value": {"resourceGroupName": "o09-rg-connectivity-p-network-weu", "vnetName": "o09-vnet-connectivity-p-weu-01", "subscriptionId": "4733f0d9-11b2-47c2-86e6-1b4657216fca", "remoteAddressSpace": ["***********/27"], "firewallIp": "***********"}}, "mgtVm": {"value": {"ipAddress": "************", "dataDisks": [], "vmSize": "Standard_D4s_v4", "volgNummer": "01", "zone": "2", "AcceleratedNetworking": "true"}}, "linuxmgtVm": {"value": {"ipAddress": "************", "dataDisks": [], "vmSize": "Standard_D2s_v5", "volgNummer": "01", "zone": "2", "AcceleratedNetworking": "true"}}, "lwpVm": {"value": {"ipAddress": "************", "dataDisks": [], "vmSize": "Standard_B2ms", "volgNummer": "01", "zone": "2", "AcceleratedNetworking": "false"}}, "helloidVm": {"value": {"ipAddress": "************", "dataDisks": [], "vmSize": "Standard_B2s", "volgNummer": "01", "zone": "2", "AcceleratedNetworking": "false"}}, "lwpstorage": {"value": {"AzureServicesDefaultAction": "Allow", "BlobPublicAccess": "true", "pContainerNames": ["configurations", "apps"], "pDataLakeEnabled": "false", "pStorageAccountName": "o09lwpuconfigs"}}, "lwfastorage": {"value": {"AzureServicesDefaultAction": "Allow", "BlobPublicAccess": "true", "pContainerNames": "flexapp", "pDataLakeEnabled": "false", "pStorageAccountName": "o09lwflexapp"}}, "intunestorage": {"value": {"AzureServicesDefaultAction": "Allow", "BlobPublicAccess": "true", "pContainerNames": "intunefiles", "pDataLakeEnabled": "false", "pStorageAccountName": "o09intunestorage"}}, "keyVaultIpAddress": {"value": "************"}, "adminUsername": {"value": "<PERSON><PERSON><PERSON>"}}}