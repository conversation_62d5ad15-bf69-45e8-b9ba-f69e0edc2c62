{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"managedByTenantId": {"type": "string", "metadata": {"description": "Add the tenant id provided by the MSP"}}, "managedByName": {"type": "string", "metadata": {"description": "Add the tenant name of the provided MSP"}}, "managedByDescription": {"type": "string", "metadata": {"description": "Add the description of the offer provided by the MSP"}}, "managedByAuthorizations": {"type": "array", "metadata": {"description": "Specify an array of objects, containing tuples of Azure Active Directory principalId, a Azure roleDefinitionId, and an optional principalIdDisplayName. The roleDefinition specified is granted to the principalId in the provider's Active Directory and the principalIdDisplayName is visible to customers."}}, "eligibleAuthorizations": {"type": "array", "metadata": {"description": "Provide the authorizations that will have just-in-time role assignments on customer environments with or without support for approvals from the managing tenant"}}}, "variables": {"policyDefinitionName": "Enable-Azure-Lighthouse", "rbacOwner": "8e3af657-a8ff-443c-a75c-2fe8c4bcb635"}, "resources": [{"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2018-05-01", "name": "[variables('policyDefinitionName')]", "properties": {"description": "Policy to enforce Lighthouse on subscriptions, delegating mgmt to MSP", "displayName": "Enforce Lighthouse on subscriptions", "mode": "All", "policyType": "Custom", "parameters": {"managedByTenantId": {"type": "string", "defaultValue": "[parameters('managedByTenantId')]", "metadata": {"description": "Add the tenant id provided by the MSP"}}, "managedByName": {"type": "string", "defaultValue": "[parameters('managedByName')]", "metadata": {"description": "Add the tenant name of the provided MSP"}}, "managedByDescription": {"type": "string", "defaultValue": "[parameters('managedByDescription')]", "metadata": {"description": "Add the description of the offer provided by the MSP"}}, "managedByAuthorizations": {"type": "array", "defaultValue": "[parameters('managedByAuthorizations')]", "metadata": {"description": "Add the authZ array provided by the MSP"}}, "eligibleAuthorizations": {"type": "array", "defaultValue": "[parameters('eligibleAuthorizations')]", "metadata": {"description": "Provide the authorizations that will have just-in-time role assignments on customer environments with support for approvals from the managing tenant"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Resources/subscriptions"}]}, "then": {"effect": "deployIfNotExists", "details": {"type": "Microsoft.ManagedServices/registrationDefinitions", "deploymentScope": "Subscription", "existenceScope": "Subscription", "roleDefinitionIds": ["[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]"], "existenceCondition": {"allOf": [{"field": "type", "equals": "Microsoft.ManagedServices/registrationDefinitions"}, {"field": "Microsoft.ManagedServices/registrationDefinitions/managedByTenantId", "equals": "[[parameters('managedByTenantId')]"}]}, "deployment": {"location": "westeurope", "properties": {"mode": "incremental", "parameters": {"managedByTenantId": {"value": "[[parameters('managedByTenantId')]"}, "managedByName": {"value": "[[parameters('managedByName')]"}, "managedByDescription": {"value": "[[parameters('managedByDescription')]"}, "managedByAuthorizations": {"value": "[[parameters('managedByAuthorizations')]"}, "eligibleAuthorizations": {"value": "[[parameters('eligibleAuthorizations')]"}}, "template": {"$schema": "https://schema.management.azure.com/2018-05-01/subscriptionDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"managedByTenantId": {"type": "string"}, "managedByName": {"type": "string"}, "managedByDescription": {"type": "string"}, "managedByAuthorizations": {"type": "array"}, "eligibleAuthorizations": {"type": "array"}}, "variables": {"managedByRegistrationName": "[[guid(parameters('managedByName'))]", "managedByAssignmentName": "[[guid(parameters('managedByName'))]"}, "resources": [{"type": "Microsoft.ManagedServices/registrationDefinitions", "apiVersion": "2022-10-01", "name": "[[variables('managedByRegistrationName')]", "properties": {"registrationDefinitionName": "[[parameters('managedByName')]", "description": "[[parameters('managedByDescription')]", "managedByTenantId": "[[parameters('managedByTenantId')]", "authorizations": "[[parameters('managedByAuthorizations')]", "eligibleAuthorizations": "[[parameters('eligibleAuthorizations')]"}}, {"type": "Microsoft.ManagedServices/registrationAssignments", "apiVersion": "2022-10-01", "name": "[[variables('managedByAssignmentName')]", "dependsOn": ["[[resourceId('Microsoft.ManagedServices/registrationDefinitions/', variables('managedByRegistrationName'))]"], "properties": {"registrationDefinitionId": "[[resourceId('Microsoft.ManagedServices/registrationDefinitions/',variables('managedByRegistrationName'))]"}}]}}}}}}}}], "outputs": {}}