{"properties": {"displayName": "Storage Account - TLS Setting AUDIT", "policyType": "Custom", "mode": "All", "description": "This Azure Policy creates an audit event when the 'Minimum TLS version' setting is not set to 'Version 1.2'.", "metadata": {"version": "1.0.0", "category": "Storage Account"}, "parameters": {"policyEffect": {"type": "String", "metadata": {"displayName": "Policy Effect", "description": "The Policy Effect associated with this Policy Definition"}, "defaultValue": "Audit"}, "tlsVersionRequired": {"type": "String", "metadata": {"displayName": "Required TLS Version", "description": "The TLS version that should be configured on the Storage Account"}, "defaultValue": "TLS1_2"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Storage/storageAccounts"}, {"field": "Microsoft.Storage/storageAccounts/minimumTlsVersion", "notEquals": "[parameters('tlsVersionRequired')]"}]}, "then": {"effect": "[parameters('policyEffect')]"}}}}