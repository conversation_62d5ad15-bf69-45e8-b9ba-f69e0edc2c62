targetScope = 'subscription'

@allowed([ 'westeurope' ])
@description('De azure location voor de resources')
param location string = 'westeurope'
param regio string = 'weu'
param klantCode  string
param workload string

@allowed([ 'o', 't', 'a', 'p' ])
@description('Beschrijft de omgeving, o voor ontwikkel, t voor test, a voor acceptatie, p voor productie')
param omgeving string

@description('''
Parameter met object vnetSettings, bevat de addressPrefixes, subnets met function en addressPrefix en dnsServers.
Voorbeeld parameter input:
"vnetSettings": {
  "value": {
    "addressPrefixes": [
      "**********/22"
    ],
    "subnets": [
      {
        "function": "subnetnaam",
        "addressPrefix": "**********/22"
      }
    ],
    "dnsServers": []
  }
}
''')
param vnetSettings object

@description('''
Parameter met gegevens van de hub voor het maken van een peering:
Bevat de resourceGroupName, de vnetName, het subscriptionId, de remoteAddressSpace.
Voorbeeld:
"hubData": {
  "value": {
    "resourceGroupName": "rg-naam",
    "vnetName": "vnet-naam",
    "subscriptionId": "dfkafa-sdasdb-4asdda-8d-asd0be",
    "remoteAddressSpace": [
      "*********/24"
    ],
    "firewallIp": "**********"
  }
}
''')
param hubData object

@description('Name of the local administrator account')
param adminUsername string

@description(
  '''
  {
      "ipAddress": "***********",
      "dataDisks": [],
      "vmSize": "Standard_F2s_v2",
      "volgNummer": "01",
      "zone": "1"
    }
  }
  ''')
param mgtVm object
param linuxmgtVm object
param lwpVm object
param helloidVm object
param lwpstorage object
param lwfastorage object
param intunestorage object
@description('Het interne ip address die assigned wordt aan het key vault voor de private endpoint')
param keyVaultIpAddress string
param remoteVirtualNetworkAddressSpace string
param beheerserverAddressPrefix array

var keyVaultTenantID = tenant().tenantId
var KeyVaultName = '${klantCode}-kv-${workload}-${omgeving}-${regio}'
var subscriptionId = subscription().subscriptionId
var managementNetworkRsgName = '${klantCode}-rg-${workload}-${omgeving}-network-${regio}'
var mgmtRsgName = '${klantCode}-rg-${workload}-${omgeving}-mgt-${regio}'
var lwRsgName = '${klantCode}-rg-${workload}-${omgeving}-liquidware-${regio}'
var synRsgName = '${klantCode}-rg-${workload}-${omgeving}-syn-${regio}'
var intuneRsgName = '${klantCode}-rg-${workload}-${omgeving}-intune-${regio}'
var rsgBackupName = '${klantCode}-rg-${workload}-${omgeving}-backup-${regio}'
var vulnerabilityRsgName = '${klantCode}-rg-vulnerabilitymanagement-p'
var keyVaultRsgName = '${klantCode}-rg-${workload}-${omgeving}-keyvault-${regio}'
var exclusionTag = 'not-in-default-vm-backup'

var securityRules = [
  {
    name: 'Allow_Inbound_Beheer_to_All_any'
    properties: {
      protocol: 'TCP'
      sourcePortRange: '*'
      destinationPortRange: 3389
      destinationAddressPrefix: vnetSettings.addressPrefixes[0]
      sourceAddressPrefixes: beheerserverAddressPrefix
      access: 'Allow'
      priority: 1000
      direction: 'Inbound'
      sourcePortRanges: []
    }
  }
  {
    name: 'Allow_inbound_to_Scom_Gateway'
    properties: {
      protocol: 'TCP'
      sourcePortRange: '*'
      destinationPortRange: 5723
      destinationApplicationSecurityGroups: [
        {
          id: asgMgt.outputs.id
          location: location
        }
      ]
      sourceAddressPrefix: '**********/23'
      access: 'Allow'
      priority: 1010
      direction: 'Inbound'
      sourcePortRanges: []
    }
  }
  {
    name: 'Allow_inbound_ramBeheer_to_mgtvm'
    properties: {
      protocol: 'TCP'
      sourcePortRange: '*'
      destinationPortRange: 3389
      destinationApplicationSecurityGroups: [
        {
          id: asgMgt.outputs.id
          location: location
        }
      ]
      sourceAddressPrefix: '*************/28'
      access: 'Allow'
      priority: 1020
      direction: 'Inbound'
      sourcePortRanges: []
    }
  }
  {
    name: 'Allow_inbound_ramBeheer_to_Scom_Gateway'
    properties: {
      protocol: 'TCP'
      sourcePortRange: '*'
      destinationPortRange: 5723
      destinationApplicationSecurityGroups: [
        {
          id: asgMgt.outputs.id
          location: location
        }
      ]
      sourceAddressPrefixes: [ 
        '**************/32'
        '**************/32'
      ]
      access: 'Allow'
      priority: 1030
      direction: 'Inbound'
      sourcePortRanges: []
    }
  }
  {
    name: 'Allow_inbound_management_to_keyvault'
    properties: {
      protocol: 'TCP'
      sourcePortRange: '*'
      destinationPortRange: 443
      destinationApplicationSecurityGroups: [
        {
          id: asgkeyvault.outputs.id
          location: location
        }
      ]
      sourceApplicationSecurityGroups: [
        {
          id: asgMgt.outputs.id
          location: location
        }
      ]
      access: 'Allow'
      priority: 1040
      direction: 'Inbound'
      sourcePortRanges: []
    }
  }
  {
    name: 'DenyAllInbound'
    properties: {
      protocol: '*'
      sourcePortRange: '*'
      destinationPortRange: '*'
      sourceAddressPrefix: '*'
      destinationAddressPrefix: '*'
      access: 'Deny'
      priority: 4096
      direction: 'Inbound'
    }
  } ]
  var routeTableName = '${klantCode}-rt-${workload}-${omgeving}-${regio}-01'
  var routes = [
    {
      name: routeTableName
      properties: {
        addressPrefix: '0.0.0.0/0'
        nextHopIpAddress: hubData.firewallIp
        nextHopType: 'VirtualAppliance'
      }
    }
  ]

resource managementNetwork_rg 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: managementNetworkRsgName
  location: location
}

resource management_backup_rg 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: rsgBackupName
  location: location
}

resource mgmt_rg 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: mgmtRsgName
  location: location
}

resource lw_rg 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: lwRsgName
  location: location
}

resource syn_rg 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: synRsgName
  location: location
}

resource intune_rg 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: intuneRsgName
  location: location
}

resource vulnerability_rg 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: vulnerabilityRsgName
  location: location
}

resource keyVault_rg 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: keyVaultRsgName
  location: location
}
module spokeVnetManagment '../Modules/spokevnet.bicep' = {
  scope: resourceGroup(managementNetwork_rg.name)
  name: 'managementVnet'
  params: {
    klantCode: klantCode
    omgeving: omgeving
    addressPrefixes: vnetSettings.addressPrefixes
    dnsServers: vnetSettings.dnsServers
    subnets: vnetSettings.subnets
    location: location
    securityRules: securityRules
    workload: workload
    regio: regio
    routes: routes
    routeTableName: routeTableName
    remoteVirtualNetworkAddressSpace: remoteVirtualNetworkAddressSpace
  }
}

module asgMgt '../Modules/applicationSecurityGroup.bicep' = {
  scope: resourceGroup(managementNetworkRsgName)
  name: 'mgtAsg'
  dependsOn:[
    managementNetwork_rg
  ]
  params: {
    function: 'mgt'
    klantCode: klantCode
    location: location
    omgeving: omgeving
    regio: regio
    workload: workload
  }

}

module asgMgtlinux '../Modules/applicationSecurityGroup.bicep' = {
  scope: resourceGroup(managementNetworkRsgName)
  name: 'mgtAsglinux'
  dependsOn:[
    managementNetwork_rg
  ]
  params: {
    function: 'mgt'
    klantCode: klantCode
    location: location
    omgeving: omgeving
    regio: regio
    workload: workload
  }

}

module asglwp '../Modules/applicationSecurityGroup.bicep' = {
  scope: resourceGroup(managementNetworkRsgName)
  name: 'lwpAsg'
  dependsOn:[
    managementNetwork_rg
  ]
  params: {
    function: 'lwp'
    klantCode: klantCode
    location: location
    omgeving: omgeving
    regio: regio
    workload: workload
  }

}

module asgsyn '../Modules/applicationSecurityGroup.bicep' = {
  scope: resourceGroup(managementNetworkRsgName)
  name: 'synAsg'
  dependsOn:[
    managementNetwork_rg
  ]
  params: {
    function: 'syn'
    klantCode: klantCode
    location: location
    omgeving: omgeving
    regio: regio
    workload: workload
  }

}

module asgkeyvault '../Modules/applicationSecurityGroup.bicep' = {
  scope: resourceGroup(managementNetworkRsgName)
  name: 'keyvaultAsg'
  dependsOn:[
    managementNetwork_rg
  ]
  params: {
    function: 'kv'
    klantCode: klantCode
    location: location
    omgeving: omgeving
    regio: regio
    workload: workload
  }

}

module mgmtserver '../Modules/windows2022-vm.bicep' = {
  scope: resourceGroup(mgmt_rg.name)
  name: 'managementvm'
  params: {
    volgNummer: mgtVm.volgNummer
    adminPassword: kv.getSecret('adminPassword')
    adminUsername: adminUsername
    functie: 'mgt'
    ipAddress: mgtVm.ipAddress
    klantCode: klantCode
    omgeving: omgeving
    subnetId: spokeVnetManagment.outputs.outSubnets[0].id
    vmSize: mgtVm.vmSize
    vmZone: mgtVm.zone
    location: location
    asgId: asgMgt.outputs.id
    dataDisks: mgtVm.dataDisks
    AcceleratedNetworking: bool(mgtVm.AcceleratedNetworking)
    dnsServers: vnetSettings.dnsServers
  }
}

module mgtserver '../Modules/debian12-vm.bicep' = {
  scope: resourceGroup(mgmt_rg.name)
  name: 'linuxmanagementvm'
  params: {
    AcceleratedNetworking: bool(linuxmgtVm.AcceleratedNetworking)
    adminPassword: kv.getSecret('adminPassword')
    adminUsername: adminUsername
    asgId: asgMgtlinux.outputs.id
    dataDisks:linuxmgtVm.dataDisks
    functie: 'mgt'
    ipAddress: linuxmgtVm.ipAddress
    klantCode: klantCode 
    omgeving: omgeving
    subnetId: spokeVnetManagment.outputs.outSubnets[0].id
    vmSize: linuxmgtVm.vmSize
    vmZone: linuxmgtVm.zone
    volgNummer: linuxmgtVm.volgNummer
    vmSku: linuxmgtVm.vmSku
    dnsServers: vnetSettings.dnsServers

  }
}

module Liquidwareserver '../Modules/windows2022-vm.bicep' = {
  scope: resourceGroup(lw_rg.name)
  name: 'liquidwarevm'
  params: {
    volgNummer: lwpVm.volgNummer
    adminPassword: kv.getSecret('adminPassword')
    adminUsername: adminUsername
    functie: 'lwp'
    ipAddress: lwpVm.ipAddress
    klantCode: klantCode
    omgeving: omgeving
    subnetId: spokeVnetManagment.outputs.outSubnets[0].id
    vmSize: lwpVm.vmSize
    vmZone: lwpVm.zone
    location: location
    asgId: asglwp.outputs.id
    dataDisks: lwpVm.dataDisks
    AcceleratedNetworking: bool(lwpVm.AcceleratedNetworking)
    dnsServers: vnetSettings.dnsServers
  }
}

module HelloIDserver '../Modules/windows2022-vm.bicep' = {
  scope: resourceGroup(syn_rg.name)
  name: 'helloidvm'
  params: {
    volgNummer: helloidVm.volgNummer
    adminPassword: kv.getSecret('adminPassword')
    adminUsername: adminUsername
    functie: 'syn'
    ipAddress: helloidVm.ipAddress
    klantCode: klantCode
    omgeving: omgeving
    subnetId: spokeVnetManagment.outputs.outSubnets[0].id
    vmSize: helloidVm.vmSize
    vmZone: helloidVm.zone
    location: location
    asgId: asgsyn.outputs.id
    dataDisks: helloidVm.dataDisks
    AcceleratedNetworking: bool(helloidVm.AcceleratedNetworking)
    dnsServers: vnetSettings.dnsServers
  }
}

module LiwuitwareStorageAccount '../Modules/StorageAccountBlob.bicep' = {
  scope: resourceGroup(lw_rg.name)
  name: 'liquidwarestorage'
  params: {
    pAllowAzureServicesDefaultAction: lwpstorage.AzureServicesDefaultAction
    pAllowBlobPublicAccess: bool(lwpstorage.BlobPublicAccess)
    pContainerNames: array(lwpstorage.pContainerNames)
    pDataLakeEnabled: bool(lwpstorage.pDataLakeEnabled)
    pLocation: location
    pStorageAccountName: lwpstorage.pStorageAccountName
  }
}

module LiwuitwareFlexAppStorageAccount '../Modules/StorageAccountBlob.bicep' = {
  scope: resourceGroup(lw_rg.name)
  name: 'liquidwareFlexAppstorage'
  params: {
    pAllowAzureServicesDefaultAction: lwfastorage.AzureServicesDefaultAction
    pAllowBlobPublicAccess: bool(lwfastorage.BlobPublicAccess)
    pContainerNames: array(lwfastorage.pContainerNames)
    pDataLakeEnabled: bool(lwfastorage.pDataLakeEnabled)
    pLocation: location
    pStorageAccountName: lwfastorage.pStorageAccountName
  }
}

module IntuneAppStorageAccount '../Modules/StorageAccountBlob.bicep' = {
  scope: resourceGroup(intune_rg.name)
  name: 'intunestorage'
  params: {
    pAllowAzureServicesDefaultAction: intunestorage.AzureServicesDefaultAction
    pAllowBlobPublicAccess: bool(intunestorage.BlobPublicAccess)
    pContainerNames: array(intunestorage.pContainerNames)
    pDataLakeEnabled: bool(intunestorage.pDataLakeEnabled)
    pLocation: location
    pStorageAccountName: intunestorage.pStorageAccountName
  }
}

module recoveryvault '../Modules/recoveryservices.bicep' = {
  scope: resourceGroup(management_backup_rg.name)
  name: 'backup'
  params: {
    klantCode:klantCode 
    location: location
    omgeving: omgeving
    workload: workload
  }
}

// Check if the policy assignment already exists
resource existingPolicyAssignment 'Microsoft.Authorization/policyAssignments@2021-06-01' existing = {
  name: '${klantCode}-VmBackup'
}
module backuppolicyassignment '../Modules/backuppolicy.bicep' = {
  name:'backuppolicy'
  params:{
    backupPolicyV2Id: recoveryvault.outputs.backupPolicyV2Id
    exclusionTag: exclusionTag
    existingPolicyAssignment: existingPolicyAssignment.name
    klantCode: klantCode 
    workload: workload
    location: location
  }
}

module privateDnsZone '../Modules/keyVaultprivateDnsZone.bicep' = {
  scope: resourceGroup(managementNetwork_rg.name)
  name: 'privateDnsZoneKeyVault'
  params: {
    vnetId: spokeVnetManagment.outputs.vnetId
  }
}
module keyVault '../Modules/keyVaultPrivateEndpoint.bicep' = {
  scope:resourceGroup(keyVault_rg.name)
  name: 'keyVaultkv'
  params:{
    keyVaultIpAddress: keyVaultIpAddress
    keyVaultName: KeyVaultName
    keyVaultTenantId: keyVaultTenantID
    privateDnsZoneId: privateDnsZone.outputs.privateDnsZoneId
    privateDnsZoneName: privateDnsZone.name
    subnetId: spokeVnetManagment.outputs.outSubnets[0].id
    location: location
    asgName:asgkeyvault.outputs.name
    asgId:asgkeyvault.outputs.id
  }
}


resource kv 'Microsoft.KeyVault/vaults@2023-07-01' existing = {
  name: KeyVaultName
  scope: resourceGroup(subscriptionId, keyVault_rg.name )
}
