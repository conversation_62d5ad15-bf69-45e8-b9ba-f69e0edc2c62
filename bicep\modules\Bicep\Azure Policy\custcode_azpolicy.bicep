targetScope = 'managementGroup'

//Parameters
param policySource string 
param policyCategory string 
param assignmentEnforcementMode string 
param CCSEC01Id string = '/providers/Microsoft.Authorization/policySetDefinitions/1f3afdf9-d0c9-4c3d-847f-89da613e70a8' //CC.SEC.01.v01 Microsoft cloud security benchmark
param CCSEC03Id string = '/providers/Microsoft.Authorization/policySetDefinitions/e20d08c5-6d64-656d-6465-ce9e37fd0ebc' //CC.SEC.03.v01  Deploy Microsoft Defender for Endpoint agent
param CCSEC04Id string = '/providers/Microsoft.Authorization/policySetDefinitions/9cb3cc7a-b39b-4b82-bc89-e5a5d9ff7b97' //CC.SEC.04.v01 Configure Azure Defender to be enabled on SQL Servers and SQL Managed Instances
param CCSEC05Id string = '/providers/Microsoft.Authorization/policySetDefinitions/77b391e3-2d5d-40c3-83bf-65c846b3c6a3' //CC.SEC.05.v01 Configure multiple Microsoft Defender for Endpoint integration settings with Microsoft Defender for Cloud

// Variables
var ccodeinitiativeName = 'Customer_Code'
var ccodeassignmentName = 'Customer_Code'
var CCSEC02file = json(loadTextContent(('./Defender for Servers P1/defenderplan1manualportal.json')))
var CCCOST01file = json(loadTextContent(('./Custcode/CC_policy/Unused_Public_IP_addresses_driving_cost_should_be_avoided.json')))
var CCCOST02file = json(loadTextContent(('./Custcode/CC_policy/Unused_Disks_driving_cost_should_be_avoided.json')))
var CCCOST03file = json(loadTextContent(('./Custcode/CC_policy/Unused_App_Service_plans_driving_cost_should_be_avoided.json')))
var CCSEC07file = json(loadTextContent(('./Custcode/CC_policy/Storage_Accounts_TLS_Audit.json')))


// OUTPUTS
output initiative1ID string = ccodeinitiative.id
output assignment1ID string = ccodeassignment.id

// Policy deffenitions
resource CCSEC02 'Microsoft.Authorization/policyDefinitions@2020-09-01' = {
  name: guid('custom','Deploy Microsoft Defender for Server Plan 1')
  properties: CCSEC02file.properties
}
resource CCSEC07 'Microsoft.Authorization/policyDefinitions@2020-09-01' = {
  name: guid('custom','Storage Account - TLS Setting AUDIT')
  properties: CCSEC07file.properties
}


resource CCCOST01 'Microsoft.Authorization/policyDefinitions@2020-09-01' = {
  name: guid('custom','Unused Public IP addresses driving cost should be avoided')
  properties: CCCOST01file.properties
}

resource CCCOST02 'Microsoft.Authorization/policyDefinitions@2020-09-01' = {
  name: guid('custom','Unused Disks driving cost should be avoided')
  properties: CCCOST02file.properties
}

resource CCCOST03 'Microsoft.Authorization/policyDefinitions@2020-09-01' = {
  name: guid('custom','Unused App Service plans driving cost should be avoided')
  properties: CCCOST03file.properties
}



// Policy deffenition sets (initiative)

// RESOURCES
resource ccodeinitiative 'Microsoft.Authorization/policySetDefinitions@2023-04-01' = {
  name: ccodeinitiativeName
  properties: {
    policyType: 'Custom'
    displayName: ccodeinitiativeName
    description: '${ccodeinitiativeName} via ${policySource}'
    metadata: {
      category: policyCategory
      source: policySource
      version: '0.0.1'
    }
    policyDefinitions: [
      /*{
        // CC.GOV.01 Lighthouse RAM Infotechnology 
        policyDefinitionId: CCGOV01.id
        policyDefinitionReferenceId: 'CC.GOV.01 Lighthouse RAM Infotechnology'
      }*/
      {
        // CC.GOV.03.v01 Locations allowed to deploy Azure resources 
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/e56962a6-4747-49cd-b67b-bf8b01975c4c'
        policyDefinitionReferenceId: 'CC.GOV.03.v01 Allowed locations'
        parameters: {
          listOfAllowedLocations: {
            value: [
              'westeurope'
            ]
          }
        }
      }
      {
        // CC.GOV.04.v01 Audit resource location matches resource group location
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a'
        policyDefinitionReferenceId: 'CC.GOV.04.v01 Audit resource location matches resource group location'
        parameters: {}
      }
      {
        // CC.SEC.02.v01 Deploy Microsoft Defender for Server Plan 1 
        policyDefinitionId: CCSEC02.id
        policyDefinitionReferenceId: 'CC.SEC.02.v01 Deploy Microsoft Defender for Server Plan 1'
      }
      {
        // CC.SEC.06.v01  Azure SQL Database should be running TLS version 1.2 or newer
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/32e6bbec-16b6-44c2-be37-c5b672d103cf'
        policyDefinitionReferenceId: 'CC.SEC.06.v01  Azure SQL Database should be running TLS version 1.2 or newer'
      }
      {
        // CC.SEC.07.v01 Audit Storage Account - TLS Setting 
        policyDefinitionId: CCSEC07.id
        policyDefinitionReferenceId: 'CC.SEC.07.v01 Audit Storage Account - TLS Setting'
      }
      {
        // CC.SEC.08.v01 Machines should have secret findings resolved
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/3ac7c827-eea2-4bde-acc7-9568cd320efa'
        policyDefinitionReferenceId: 'CC.SEC.08.v01 Machines should have secret findings resolved'
      }
      {
        // CC.SEC.09v01 Internet-facing virtual machines should be protected with network security groups
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/f6de0be7-9a8a-4b8a-b349-43cf02d22f7c'
        policyDefinitionReferenceId: 'CC.SEC.09v01 Internet-facing virtual machines should be protected with network security groups'
      }
      {
        // CC.SEC.10.v01 Non-internet-facing virtual machines should be protected with network security groups
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/bb91dfba-c30d-4263-9add-9c2384e659a6'
        policyDefinitionReferenceId: 'CC.SEC.10.v01 Non-internet-facing virtual machines should be protected with network security groups'
      }
      {
        // CC.IAM.01.v01 Accounts with read permissions on Azure resources should be MFA enabled
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/81b3ccb4-e6e8-4e4a-8d05-5df25cd29fd4'
        policyDefinitionReferenceId: 'CC.IAM.01.v01 Accounts with read permissions on Azure resources should be MFA enabled'
        parameters: {}
      }
      {
        // CC.IAM.02.v01 Accounts with write permissions on Azure resources should be MFA enabled
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/931e118d-50a1-4457-a5e4-78550e086c52'
        policyDefinitionReferenceId: 'CC.IAM.02.v01 Accounts with write permissions on Azure resources should be MFA enabled'
        parameters: {}
      }
      {
        // CC.IAM.03.v01 Blocked accounts with owner permissions on Azure resources should be removed
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/0cfea604-3201-4e14-88fc-fae4c427a6c5'
        policyDefinitionReferenceId: 'CC.IAM.03.v01 Blocked accounts with owner permissions on Azure resources should be removed'
        parameters: {}
      }
      {
        // CC.IAM.04.v01 Blocked accounts with read and write permissions on Azure resources should be removed
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/8d7e1fde-fe26-4b5f-8108-f8e432cbc2be'
        policyDefinitionReferenceId: 'CC.IAM.04.v01 Blocked accounts with read and write permissions on Azure resources should be removed'
        parameters: {}
      }
      {
        // CC.COMP.02.v01 Virtual machines Guest Configuration extension should be deployed with system-assigned managed identity
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/d26f7642-7545-4e18-9b75-8c9bbdee3a9a'
        policyDefinitionReferenceId: 'CC.COMP.01.v01 Virtual machines Guest Configuration extension should be deployed with system-assigned managed identity'
        parameters: {}
      }
      {
        // CC.COMP.03.v01 Subnets should be associated with a Network Security Group
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/e71308d3-144b-4262-b144-efdc3cc90517'
        policyDefinitionReferenceId: 'CC.COMP.02.v01 Subnets should be associated with a Network Security Group'
        parameters: {}
      }
      {
        // CC.COMP.04.v01 Audit TrustedLaunch
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf'
        policyDefinitionReferenceId: 'CC.COMP.04.v01 Audit TrustedLaunch'
        parameters: {}
      }
      {
        // CC.COMP.05.v01 Deny Virtual Machines and Virtual Machine Scale Sets from not using OS Managed Disks
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d'
        policyDefinitionReferenceId: 'CC.COMP.05.v01 Deny Virtual Machines and Virtual Machine Scale Sets from not using OS Managed Disks'
        parameters: {}
      }
      {
        // CC.COMP.06.v01 [Preview]: Guest Attestation extension should be installed on supported Windows virtual machines
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/1cb4d9c2-f88f-4069-bee0-dba239a57b09'
        policyDefinitionReferenceId: 'CC.COMP.06.v01 [Preview]: Guest Attestation extension should be installed on supported Windows virtual machines'
        parameters: {}
      }
      {
        // CC.COMP.07v01 [Preview]: Guest Attestation extension should be installed on supported Linux virtual machines
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/672fe5a1-2fcd-42d7-b85d-902b6e28c6ff'
        policyDefinitionReferenceId: 'CC.COMP.07v01 [Preview]: Guest Attestation extension should be installed on supported Linux virtual machines'
        parameters: {}
      }
      {
        // CC.COMP.08.v01 Windows virtual machines should enable Azure Disk Encryption or EncryptionAtHost
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/3dc5edcd-002d-444c-b216-e123bbfa37c0'
        policyDefinitionReferenceId: 'CC.COMP.08.v01 Windows virtual machines should enable Azure Disk Encryption or EncryptionAtHost'
        parameters: {}
      }
      {
        // CC.COMP.09.v01 Linux virtual machines should enable Azure Disk Encryption or EncryptionAtHost
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/ca88aadc-6e2b-416c-9de2-5a0f01d1693f'
        policyDefinitionReferenceId: 'CC.COMP.09.v01 Linux virtual machines should enable Azure Disk Encryption or EncryptionAtHost'
        parameters: {}
      }
      {
        // CC.COMP.10.v01 Audit Windows machines that have not restarted within the specified number of days
        policyDefinitionId: '/providers/Microsoft.Authorization/policyDefinitions/beb6ccee-b6b8-4e91-9801-a5fa4260a104'
        policyDefinitionReferenceId: 'CC.COMP.10.v01 Audit Windows machines that have not restarted within the specified number of days'
        parameters: {}
      }
      {
        // CC.COST.01v01 Unused Public IP addresses driving cost should be avoided
        policyDefinitionId: CCCOST01.id
        policyDefinitionReferenceId: 'CC.COST.01v01 Unused Public IP addresses driving cost should be avoided'
        parameters: {}
      }
      {
        // CC.COST.02.v01 Unused Disks driving cost should be avoided
        policyDefinitionId: CCCOST02.id
        policyDefinitionReferenceId: 'CC.COST.02.v01 Unused Disks driving cost should be avoided'
        parameters: {}
      }
      {
        // CC.COST.03.v01 Unused App Service plans driving cost should be avoided
        policyDefinitionId: CCCOST03.id
        policyDefinitionReferenceId: 'CC.COST.03.v01 Unused App Service plans driving cost should be avoided'
        parameters: {}
      }
    ]
  }
}

resource ccodeassignment 'Microsoft.Authorization/policyAssignments@2023-04-01' = {
      name: ccodeassignmentName
      location: 'west europe'
      identity:{
       type:'SystemAssigned'
      }
      properties: {
        displayName: ccodeassignmentName
        description: '${ccodeassignmentName} via ${policySource}'
        enforcementMode: assignmentEnforcementMode
        metadata: {
          source: policySource
          version: '0.0.1'
        }
        policyDefinitionId: ccodeinitiative.id      
      }
}

resource CCSEC01 'Microsoft.Authorization/policyAssignments@2021-06-01' = {
  name: 'CCSEC01v01' //audit
  properties: {
    displayName: 'CC.SEC.01.v01 Microsoft cloud security benchmark'
    policyDefinitionId: CCSEC01Id
    enforcementMode: assignmentEnforcementMode
  }
}

resource CCSEC03 'Microsoft.Authorization/policyAssignments@2021-06-01' = {
  name: 'CCSEC03v01' //deployifnotexist
  location: 'west europe'
  identity:{
   type:'SystemAssigned'
  }
  properties: {
    displayName: 'CC.SEC.03.v01  Deploy Microsoft Defender for Endpoint agent'
    policyDefinitionId: CCSEC03Id
    enforcementMode: assignmentEnforcementMode
  }
}

resource CCSEC04 'Microsoft.Authorization/policyAssignments@2021-06-01' = {
  name: 'CCSEC04v01' //deployifnotexist
  location: 'west europe'
  identity:{
   type:'SystemAssigned'
  }
  properties: {
    displayName: 'CC.SEC.04.v01 Configure Azure Defender to be enabled on SQL Servers and SQL Managed Instances'
    policyDefinitionId: CCSEC04Id
    enforcementMode: assignmentEnforcementMode
  }
}

resource CCSEC05 'Microsoft.Authorization/policyAssignments@2021-06-01' = {
  name: 'CCSEC05v01' //deployifnotexist
  location: 'west europe'
  identity:{
   type:'SystemAssigned'
  }
  properties: {
    displayName: 'CC.SEC.05.v01 Configure multiple Microsoft Defender for Endpoint integration settings with Microsoft Defender for Cloud'
    policyDefinitionId: CCSEC05Id
    enforcementMode: assignmentEnforcementMode
  }
}



